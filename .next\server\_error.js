/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "/_error";
exports.ids = ["/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules\\next\\dist\\pages\\_error.js */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\nprivate_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_uiStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../store/uiStore */ \"./store/uiStore.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_store_uiStore__WEBPACK_IMPORTED_MODULE_3__]);\n_store_uiStore__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// Main app wrapper with providers and global setup\n\n\n\n\nfunction App({ Component, pageProps }) {\n    const { setTheme } = (0,_store_uiStore__WEBPACK_IMPORTED_MODULE_3__.useUIStore)();\n    // Initialize theme on app load\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Check for saved theme preference or default to 'light'\n        const savedTheme = localStorage.getItem(\"theme\") || \"light\";\n        setTheme(savedTheme);\n    }, [\n        setTheme\n    ]);\n    // Global error boundary\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const handleError = (event)=>{\n            console.error(\"Global error:\", event.error);\n        // You could add error reporting here\n        };\n        const handleUnhandledRejection = (event)=>{\n            console.error(\"Unhandled promise rejection:\", event.reason);\n        // You could add error reporting here\n        };\n        window.addEventListener(\"error\", handleError);\n        window.addEventListener(\"unhandledrejection\", handleUnhandledRejection);\n        return ()=>{\n            window.removeEventListener(\"error\", handleError);\n            window.removeEventListener(\"unhandledrejection\", handleUnhandledRejection);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\_app.js\",\n        lineNumber: 37,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./store/uiStore.js":
/*!**************************!*\
  !*** ./store/uiStore.js ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUIStore: () => (/* binding */ useUIStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"zustand\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zustand__WEBPACK_IMPORTED_MODULE_0__]);\nzustand__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// Zustand store for UI state management\n\nconst useUIStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        // Sidebar state\n        sidebarOpen: true,\n        // Modal states\n        taskModalOpen: false,\n        taskModalData: null,\n        // AI Assistant state\n        aiAssistantOpen: false,\n        // Calendar state\n        calendarView: \"month\",\n        selectedDate: new Date(),\n        // Theme and preferences\n        theme: \"light\",\n        compactMode: false,\n        // Notification state\n        notifications: [],\n        // Loading states\n        globalLoading: false,\n        // Actions\n        toggleSidebar: ()=>set((state)=>({\n                    sidebarOpen: !state.sidebarOpen\n                })),\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        // Task modal actions\n        openTaskModal: (taskData = null)=>set({\n                taskModalOpen: true,\n                taskModalData: taskData\n            }),\n        closeTaskModal: ()=>set({\n                taskModalOpen: false,\n                taskModalData: null\n            }),\n        // AI Assistant actions\n        toggleAIAssistant: ()=>set((state)=>({\n                    aiAssistantOpen: !state.aiAssistantOpen\n                })),\n        setAIAssistantOpen: (open)=>set({\n                aiAssistantOpen: open\n            }),\n        // Calendar actions\n        setCalendarView: (view)=>set({\n                calendarView: view\n            }),\n        setSelectedDate: (date)=>set({\n                selectedDate: date\n            }),\n        // Theme actions\n        setTheme: (theme)=>{\n            set({\n                theme\n            });\n            // Apply theme to document\n            if (false) {}\n        },\n        toggleCompactMode: ()=>set((state)=>({\n                    compactMode: !state.compactMode\n                })),\n        // Notification actions\n        addNotification: (notification)=>{\n            const id = Date.now().toString();\n            const newNotification = {\n                id,\n                type: \"info\",\n                title: \"\",\n                message: \"\",\n                duration: 5000,\n                ...notification\n            };\n            set((state)=>({\n                    notifications: [\n                        ...state.notifications,\n                        newNotification\n                    ]\n                }));\n            // Auto-remove notification after duration\n            if (newNotification.duration > 0) {\n                setTimeout(()=>{\n                    get().removeNotification(id);\n                }, newNotification.duration);\n            }\n            return id;\n        },\n        removeNotification: (id)=>set((state)=>({\n                    notifications: state.notifications.filter((n)=>n.id !== id)\n                })),\n        clearNotifications: ()=>set({\n                notifications: []\n            }),\n        // Global loading\n        setGlobalLoading: (loading)=>set({\n                globalLoading: loading\n            }),\n        // Keyboard shortcuts state\n        shortcutsEnabled: true,\n        toggleShortcuts: ()=>set((state)=>({\n                    shortcutsEnabled: !state.shortcutsEnabled\n                })),\n        // Search state\n        searchQuery: \"\",\n        searchResults: [],\n        searchOpen: false,\n        setSearchQuery: (query)=>set({\n                searchQuery: query\n            }),\n        setSearchResults: (results)=>set({\n                searchResults: results\n            }),\n        toggleSearch: ()=>set((state)=>({\n                    searchOpen: !state.searchOpen\n                })),\n        // View preferences\n        viewPreferences: {\n            kanban: {\n                columnsVisible: [\n                    \"todo\",\n                    \"in-progress\",\n                    \"review\",\n                    \"done\"\n                ],\n                compactCards: false\n            },\n            todo: {\n                showCompleted: false,\n                groupBy: \"none\" // none, priority, category, dueDate\n            },\n            calendar: {\n                showWeekends: true,\n                startHour: 8,\n                endHour: 18\n            }\n        },\n        updateViewPreference: (view, key, value)=>set((state)=>({\n                    viewPreferences: {\n                        ...state.viewPreferences,\n                        [view]: {\n                            ...state.viewPreferences[view],\n                            [key]: value\n                        }\n                    }\n                })),\n        // Drag and drop state\n        dragState: {\n            isDragging: false,\n            draggedItem: null,\n            dropZone: null\n        },\n        setDragState: (dragState)=>set({\n                dragState\n            }),\n        // Focus mode\n        focusMode: false,\n        focusTask: null,\n        enterFocusMode: (task = null)=>set({\n                focusMode: true,\n                focusTask: task,\n                sidebarOpen: false,\n                aiAssistantOpen: false\n            }),\n        exitFocusMode: ()=>set({\n                focusMode: false,\n                focusTask: null\n            }),\n        // Reset all UI state\n        resetUIState: ()=>set({\n                sidebarOpen: true,\n                taskModalOpen: false,\n                taskModalData: null,\n                aiAssistantOpen: false,\n                searchOpen: false,\n                searchQuery: \"\",\n                searchResults: [],\n                focusMode: false,\n                focusTask: null,\n                notifications: []\n            })\n    }));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zdG9yZS91aVN0b3JlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUEsd0NBQXdDO0FBQ1A7QUFFMUIsTUFBTUMsYUFBYUQsK0NBQU1BLENBQUMsQ0FBQ0UsS0FBS0MsTUFBUztRQUM5QyxnQkFBZ0I7UUFDaEJDLGFBQWE7UUFFYixlQUFlO1FBQ2ZDLGVBQWU7UUFDZkMsZUFBZTtRQUVmLHFCQUFxQjtRQUNyQkMsaUJBQWlCO1FBRWpCLGlCQUFpQjtRQUNqQkMsY0FBYztRQUNkQyxjQUFjLElBQUlDO1FBRWxCLHdCQUF3QjtRQUN4QkMsT0FBTztRQUNQQyxhQUFhO1FBRWIscUJBQXFCO1FBQ3JCQyxlQUFlLEVBQUU7UUFFakIsaUJBQWlCO1FBQ2pCQyxlQUFlO1FBRWYsVUFBVTtRQUNWQyxlQUFlLElBQU1iLElBQUksQ0FBQ2MsUUFBVztvQkFBRVosYUFBYSxDQUFDWSxNQUFNWixXQUFXO2dCQUFDO1FBQ3ZFYSxnQkFBZ0IsQ0FBQ0MsT0FBU2hCLElBQUk7Z0JBQUVFLGFBQWFjO1lBQUs7UUFFbEQscUJBQXFCO1FBQ3JCQyxlQUFlLENBQUNDLFdBQVcsSUFBSSxHQUFLbEIsSUFBSTtnQkFDdENHLGVBQWU7Z0JBQ2ZDLGVBQWVjO1lBQ2pCO1FBQ0FDLGdCQUFnQixJQUFNbkIsSUFBSTtnQkFDeEJHLGVBQWU7Z0JBQ2ZDLGVBQWU7WUFDakI7UUFFQSx1QkFBdUI7UUFDdkJnQixtQkFBbUIsSUFBTXBCLElBQUksQ0FBQ2MsUUFBVztvQkFDdkNULGlCQUFpQixDQUFDUyxNQUFNVCxlQUFlO2dCQUN6QztRQUNBZ0Isb0JBQW9CLENBQUNMLE9BQVNoQixJQUFJO2dCQUFFSyxpQkFBaUJXO1lBQUs7UUFFMUQsbUJBQW1CO1FBQ25CTSxpQkFBaUIsQ0FBQ0MsT0FBU3ZCLElBQUk7Z0JBQUVNLGNBQWNpQjtZQUFLO1FBQ3BEQyxpQkFBaUIsQ0FBQ0MsT0FBU3pCLElBQUk7Z0JBQUVPLGNBQWNrQjtZQUFLO1FBRXBELGdCQUFnQjtRQUNoQkMsVUFBVSxDQUFDakI7WUFDVFQsSUFBSTtnQkFBRVM7WUFBTTtZQUNaLDBCQUEwQjtZQUMxQixJQUFJLEtBQWtCLEVBQWEsRUFNbEM7UUFDSDtRQUVBdUIsbUJBQW1CLElBQU1oQyxJQUFJLENBQUNjLFFBQVc7b0JBQUVKLGFBQWEsQ0FBQ0ksTUFBTUosV0FBVztnQkFBQztRQUUzRSx1QkFBdUI7UUFDdkJ1QixpQkFBaUIsQ0FBQ0M7WUFDaEIsTUFBTUMsS0FBSzNCLEtBQUs0QixHQUFHLEdBQUdDLFFBQVE7WUFDOUIsTUFBTUMsa0JBQWtCO2dCQUN0Qkg7Z0JBQ0FJLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLFVBQVU7Z0JBQ1YsR0FBR1IsWUFBWTtZQUNqQjtZQUVBbEMsSUFBSSxDQUFDYyxRQUFXO29CQUNkSCxlQUFlOzJCQUFJRyxNQUFNSCxhQUFhO3dCQUFFMkI7cUJBQWdCO2dCQUMxRDtZQUVBLDBDQUEwQztZQUMxQyxJQUFJQSxnQkFBZ0JJLFFBQVEsR0FBRyxHQUFHO2dCQUNoQ0MsV0FBVztvQkFDVDFDLE1BQU0yQyxrQkFBa0IsQ0FBQ1Q7Z0JBQzNCLEdBQUdHLGdCQUFnQkksUUFBUTtZQUM3QjtZQUVBLE9BQU9QO1FBQ1Q7UUFFQVMsb0JBQW9CLENBQUNULEtBQU9uQyxJQUFJLENBQUNjLFFBQVc7b0JBQzFDSCxlQUFlRyxNQUFNSCxhQUFhLENBQUNrQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVYLEVBQUUsS0FBS0E7Z0JBQzFEO1FBRUFZLG9CQUFvQixJQUFNL0MsSUFBSTtnQkFBRVcsZUFBZSxFQUFFO1lBQUM7UUFFbEQsaUJBQWlCO1FBQ2pCcUMsa0JBQWtCLENBQUNDLFVBQVlqRCxJQUFJO2dCQUFFWSxlQUFlcUM7WUFBUTtRQUU1RCwyQkFBMkI7UUFDM0JDLGtCQUFrQjtRQUNsQkMsaUJBQWlCLElBQU1uRCxJQUFJLENBQUNjLFFBQVc7b0JBQ3JDb0Msa0JBQWtCLENBQUNwQyxNQUFNb0MsZ0JBQWdCO2dCQUMzQztRQUVBLGVBQWU7UUFDZkUsYUFBYTtRQUNiQyxlQUFlLEVBQUU7UUFDakJDLFlBQVk7UUFFWkMsZ0JBQWdCLENBQUNDLFFBQVV4RCxJQUFJO2dCQUFFb0QsYUFBYUk7WUFBTTtRQUNwREMsa0JBQWtCLENBQUNDLFVBQVkxRCxJQUFJO2dCQUFFcUQsZUFBZUs7WUFBUTtRQUM1REMsY0FBYyxJQUFNM0QsSUFBSSxDQUFDYyxRQUFXO29CQUFFd0MsWUFBWSxDQUFDeEMsTUFBTXdDLFVBQVU7Z0JBQUM7UUFFcEUsbUJBQW1CO1FBQ25CTSxpQkFBaUI7WUFDZkMsUUFBUTtnQkFDTkMsZ0JBQWdCO29CQUFDO29CQUFRO29CQUFlO29CQUFVO2lCQUFPO2dCQUN6REMsY0FBYztZQUNoQjtZQUNBQyxNQUFNO2dCQUNKQyxlQUFlO2dCQUNmQyxTQUFTLE9BQU8sb0NBQW9DO1lBQ3REO1lBQ0FDLFVBQVU7Z0JBQ1JDLGNBQWM7Z0JBQ2RDLFdBQVc7Z0JBQ1hDLFNBQVM7WUFDWDtRQUNGO1FBRUFDLHNCQUFzQixDQUFDaEQsTUFBTWlELEtBQUtDLFFBQVV6RSxJQUFJLENBQUNjLFFBQVc7b0JBQzFEOEMsaUJBQWlCO3dCQUNmLEdBQUc5QyxNQUFNOEMsZUFBZTt3QkFDeEIsQ0FBQ3JDLEtBQUssRUFBRTs0QkFDTixHQUFHVCxNQUFNOEMsZUFBZSxDQUFDckMsS0FBSzs0QkFDOUIsQ0FBQ2lELElBQUksRUFBRUM7d0JBQ1Q7b0JBQ0Y7Z0JBQ0Y7UUFFQSxzQkFBc0I7UUFDdEJDLFdBQVc7WUFDVEMsWUFBWTtZQUNaQyxhQUFhO1lBQ2JDLFVBQVU7UUFDWjtRQUVBQyxjQUFjLENBQUNKLFlBQWMxRSxJQUFJO2dCQUFFMEU7WUFBVTtRQUU3QyxhQUFhO1FBQ2JLLFdBQVc7UUFDWEMsV0FBVztRQUVYQyxnQkFBZ0IsQ0FBQ0MsT0FBTyxJQUFJLEdBQUtsRixJQUFJO2dCQUNuQytFLFdBQVc7Z0JBQ1hDLFdBQVdFO2dCQUNYaEYsYUFBYTtnQkFDYkcsaUJBQWlCO1lBQ25CO1FBRUE4RSxlQUFlLElBQU1uRixJQUFJO2dCQUN2QitFLFdBQVc7Z0JBQ1hDLFdBQVc7WUFDYjtRQUVBLHFCQUFxQjtRQUNyQkksY0FBYyxJQUFNcEYsSUFBSTtnQkFDdEJFLGFBQWE7Z0JBQ2JDLGVBQWU7Z0JBQ2ZDLGVBQWU7Z0JBQ2ZDLGlCQUFpQjtnQkFDakJpRCxZQUFZO2dCQUNaRixhQUFhO2dCQUNiQyxlQUFlLEVBQUU7Z0JBQ2pCMEIsV0FBVztnQkFDWEMsV0FBVztnQkFDWHJFLGVBQWUsRUFBRTtZQUNuQjtJQUNGLElBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb2N1c2Zsb3ctYWkvLi9zdG9yZS91aVN0b3JlLmpzPzk2NTUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gWnVzdGFuZCBzdG9yZSBmb3IgVUkgc3RhdGUgbWFuYWdlbWVudFxyXG5pbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJztcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VVSVN0b3JlID0gY3JlYXRlKChzZXQsIGdldCkgPT4gKHtcclxuICAvLyBTaWRlYmFyIHN0YXRlXHJcbiAgc2lkZWJhck9wZW46IHRydWUsXHJcbiAgXHJcbiAgLy8gTW9kYWwgc3RhdGVzXHJcbiAgdGFza01vZGFsT3BlbjogZmFsc2UsXHJcbiAgdGFza01vZGFsRGF0YTogbnVsbCxcclxuICBcclxuICAvLyBBSSBBc3Npc3RhbnQgc3RhdGVcclxuICBhaUFzc2lzdGFudE9wZW46IGZhbHNlLFxyXG4gIFxyXG4gIC8vIENhbGVuZGFyIHN0YXRlXHJcbiAgY2FsZW5kYXJWaWV3OiAnbW9udGgnLCAvLyBtb250aCwgd2VlaywgZGF5XHJcbiAgc2VsZWN0ZWREYXRlOiBuZXcgRGF0ZSgpLFxyXG4gIFxyXG4gIC8vIFRoZW1lIGFuZCBwcmVmZXJlbmNlc1xyXG4gIHRoZW1lOiAnbGlnaHQnLCAvLyBsaWdodCwgZGFyaywgc3lzdGVtXHJcbiAgY29tcGFjdE1vZGU6IGZhbHNlLFxyXG4gIFxyXG4gIC8vIE5vdGlmaWNhdGlvbiBzdGF0ZVxyXG4gIG5vdGlmaWNhdGlvbnM6IFtdLFxyXG4gIFxyXG4gIC8vIExvYWRpbmcgc3RhdGVzXHJcbiAgZ2xvYmFsTG9hZGluZzogZmFsc2UsXHJcbiAgXHJcbiAgLy8gQWN0aW9uc1xyXG4gIHRvZ2dsZVNpZGViYXI6ICgpID0+IHNldCgoc3RhdGUpID0+ICh7IHNpZGViYXJPcGVuOiAhc3RhdGUuc2lkZWJhck9wZW4gfSkpLFxyXG4gIHNldFNpZGViYXJPcGVuOiAob3BlbikgPT4gc2V0KHsgc2lkZWJhck9wZW46IG9wZW4gfSksXHJcbiAgXHJcbiAgLy8gVGFzayBtb2RhbCBhY3Rpb25zXHJcbiAgb3BlblRhc2tNb2RhbDogKHRhc2tEYXRhID0gbnVsbCkgPT4gc2V0KHsgXHJcbiAgICB0YXNrTW9kYWxPcGVuOiB0cnVlLCBcclxuICAgIHRhc2tNb2RhbERhdGE6IHRhc2tEYXRhIFxyXG4gIH0pLFxyXG4gIGNsb3NlVGFza01vZGFsOiAoKSA9PiBzZXQoeyBcclxuICAgIHRhc2tNb2RhbE9wZW46IGZhbHNlLCBcclxuICAgIHRhc2tNb2RhbERhdGE6IG51bGwgXHJcbiAgfSksXHJcbiAgXHJcbiAgLy8gQUkgQXNzaXN0YW50IGFjdGlvbnNcclxuICB0b2dnbGVBSUFzc2lzdGFudDogKCkgPT4gc2V0KChzdGF0ZSkgPT4gKHsgXHJcbiAgICBhaUFzc2lzdGFudE9wZW46ICFzdGF0ZS5haUFzc2lzdGFudE9wZW4gXHJcbiAgfSkpLFxyXG4gIHNldEFJQXNzaXN0YW50T3BlbjogKG9wZW4pID0+IHNldCh7IGFpQXNzaXN0YW50T3Blbjogb3BlbiB9KSxcclxuICBcclxuICAvLyBDYWxlbmRhciBhY3Rpb25zXHJcbiAgc2V0Q2FsZW5kYXJWaWV3OiAodmlldykgPT4gc2V0KHsgY2FsZW5kYXJWaWV3OiB2aWV3IH0pLFxyXG4gIHNldFNlbGVjdGVkRGF0ZTogKGRhdGUpID0+IHNldCh7IHNlbGVjdGVkRGF0ZTogZGF0ZSB9KSxcclxuICBcclxuICAvLyBUaGVtZSBhY3Rpb25zXHJcbiAgc2V0VGhlbWU6ICh0aGVtZSkgPT4ge1xyXG4gICAgc2V0KHsgdGhlbWUgfSk7XHJcbiAgICAvLyBBcHBseSB0aGVtZSB0byBkb2N1bWVudFxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgIGlmICh0aGVtZSA9PT0gJ2RhcmsnKSB7XHJcbiAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5hZGQoJ2RhcmsnKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LnJlbW92ZSgnZGFyaycpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSxcclxuICBcclxuICB0b2dnbGVDb21wYWN0TW9kZTogKCkgPT4gc2V0KChzdGF0ZSkgPT4gKHsgY29tcGFjdE1vZGU6ICFzdGF0ZS5jb21wYWN0TW9kZSB9KSksXHJcbiAgXHJcbiAgLy8gTm90aWZpY2F0aW9uIGFjdGlvbnNcclxuICBhZGROb3RpZmljYXRpb246IChub3RpZmljYXRpb24pID0+IHtcclxuICAgIGNvbnN0IGlkID0gRGF0ZS5ub3coKS50b1N0cmluZygpO1xyXG4gICAgY29uc3QgbmV3Tm90aWZpY2F0aW9uID0ge1xyXG4gICAgICBpZCxcclxuICAgICAgdHlwZTogJ2luZm8nLCAvLyBpbmZvLCBzdWNjZXNzLCB3YXJuaW5nLCBlcnJvclxyXG4gICAgICB0aXRsZTogJycsXHJcbiAgICAgIG1lc3NhZ2U6ICcnLFxyXG4gICAgICBkdXJhdGlvbjogNTAwMCxcclxuICAgICAgLi4ubm90aWZpY2F0aW9uXHJcbiAgICB9O1xyXG4gICAgXHJcbiAgICBzZXQoKHN0YXRlKSA9PiAoe1xyXG4gICAgICBub3RpZmljYXRpb25zOiBbLi4uc3RhdGUubm90aWZpY2F0aW9ucywgbmV3Tm90aWZpY2F0aW9uXVxyXG4gICAgfSkpO1xyXG4gICAgXHJcbiAgICAvLyBBdXRvLXJlbW92ZSBub3RpZmljYXRpb24gYWZ0ZXIgZHVyYXRpb25cclxuICAgIGlmIChuZXdOb3RpZmljYXRpb24uZHVyYXRpb24gPiAwKSB7XHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIGdldCgpLnJlbW92ZU5vdGlmaWNhdGlvbihpZCk7XHJcbiAgICAgIH0sIG5ld05vdGlmaWNhdGlvbi5kdXJhdGlvbik7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBpZDtcclxuICB9LFxyXG4gIFxyXG4gIHJlbW92ZU5vdGlmaWNhdGlvbjogKGlkKSA9PiBzZXQoKHN0YXRlKSA9PiAoe1xyXG4gICAgbm90aWZpY2F0aW9uczogc3RhdGUubm90aWZpY2F0aW9ucy5maWx0ZXIobiA9PiBuLmlkICE9PSBpZClcclxuICB9KSksXHJcbiAgXHJcbiAgY2xlYXJOb3RpZmljYXRpb25zOiAoKSA9PiBzZXQoeyBub3RpZmljYXRpb25zOiBbXSB9KSxcclxuICBcclxuICAvLyBHbG9iYWwgbG9hZGluZ1xyXG4gIHNldEdsb2JhbExvYWRpbmc6IChsb2FkaW5nKSA9PiBzZXQoeyBnbG9iYWxMb2FkaW5nOiBsb2FkaW5nIH0pLFxyXG4gIFxyXG4gIC8vIEtleWJvYXJkIHNob3J0Y3V0cyBzdGF0ZVxyXG4gIHNob3J0Y3V0c0VuYWJsZWQ6IHRydWUsXHJcbiAgdG9nZ2xlU2hvcnRjdXRzOiAoKSA9PiBzZXQoKHN0YXRlKSA9PiAoeyBcclxuICAgIHNob3J0Y3V0c0VuYWJsZWQ6ICFzdGF0ZS5zaG9ydGN1dHNFbmFibGVkIFxyXG4gIH0pKSxcclxuICBcclxuICAvLyBTZWFyY2ggc3RhdGVcclxuICBzZWFyY2hRdWVyeTogJycsXHJcbiAgc2VhcmNoUmVzdWx0czogW10sXHJcbiAgc2VhcmNoT3BlbjogZmFsc2UsXHJcbiAgXHJcbiAgc2V0U2VhcmNoUXVlcnk6IChxdWVyeSkgPT4gc2V0KHsgc2VhcmNoUXVlcnk6IHF1ZXJ5IH0pLFxyXG4gIHNldFNlYXJjaFJlc3VsdHM6IChyZXN1bHRzKSA9PiBzZXQoeyBzZWFyY2hSZXN1bHRzOiByZXN1bHRzIH0pLFxyXG4gIHRvZ2dsZVNlYXJjaDogKCkgPT4gc2V0KChzdGF0ZSkgPT4gKHsgc2VhcmNoT3BlbjogIXN0YXRlLnNlYXJjaE9wZW4gfSkpLFxyXG4gIFxyXG4gIC8vIFZpZXcgcHJlZmVyZW5jZXNcclxuICB2aWV3UHJlZmVyZW5jZXM6IHtcclxuICAgIGthbmJhbjoge1xyXG4gICAgICBjb2x1bW5zVmlzaWJsZTogWyd0b2RvJywgJ2luLXByb2dyZXNzJywgJ3JldmlldycsICdkb25lJ10sXHJcbiAgICAgIGNvbXBhY3RDYXJkczogZmFsc2VcclxuICAgIH0sXHJcbiAgICB0b2RvOiB7XHJcbiAgICAgIHNob3dDb21wbGV0ZWQ6IGZhbHNlLFxyXG4gICAgICBncm91cEJ5OiAnbm9uZScgLy8gbm9uZSwgcHJpb3JpdHksIGNhdGVnb3J5LCBkdWVEYXRlXHJcbiAgICB9LFxyXG4gICAgY2FsZW5kYXI6IHtcclxuICAgICAgc2hvd1dlZWtlbmRzOiB0cnVlLFxyXG4gICAgICBzdGFydEhvdXI6IDgsXHJcbiAgICAgIGVuZEhvdXI6IDE4XHJcbiAgICB9XHJcbiAgfSxcclxuICBcclxuICB1cGRhdGVWaWV3UHJlZmVyZW5jZTogKHZpZXcsIGtleSwgdmFsdWUpID0+IHNldCgoc3RhdGUpID0+ICh7XHJcbiAgICB2aWV3UHJlZmVyZW5jZXM6IHtcclxuICAgICAgLi4uc3RhdGUudmlld1ByZWZlcmVuY2VzLFxyXG4gICAgICBbdmlld106IHtcclxuICAgICAgICAuLi5zdGF0ZS52aWV3UHJlZmVyZW5jZXNbdmlld10sXHJcbiAgICAgICAgW2tleV06IHZhbHVlXHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9KSksXHJcbiAgXHJcbiAgLy8gRHJhZyBhbmQgZHJvcCBzdGF0ZVxyXG4gIGRyYWdTdGF0ZToge1xyXG4gICAgaXNEcmFnZ2luZzogZmFsc2UsXHJcbiAgICBkcmFnZ2VkSXRlbTogbnVsbCxcclxuICAgIGRyb3Bab25lOiBudWxsXHJcbiAgfSxcclxuICBcclxuICBzZXREcmFnU3RhdGU6IChkcmFnU3RhdGUpID0+IHNldCh7IGRyYWdTdGF0ZSB9KSxcclxuICBcclxuICAvLyBGb2N1cyBtb2RlXHJcbiAgZm9jdXNNb2RlOiBmYWxzZSxcclxuICBmb2N1c1Rhc2s6IG51bGwsXHJcbiAgXHJcbiAgZW50ZXJGb2N1c01vZGU6ICh0YXNrID0gbnVsbCkgPT4gc2V0KHsgXHJcbiAgICBmb2N1c01vZGU6IHRydWUsIFxyXG4gICAgZm9jdXNUYXNrOiB0YXNrLFxyXG4gICAgc2lkZWJhck9wZW46IGZhbHNlLFxyXG4gICAgYWlBc3Npc3RhbnRPcGVuOiBmYWxzZVxyXG4gIH0pLFxyXG4gIFxyXG4gIGV4aXRGb2N1c01vZGU6ICgpID0+IHNldCh7IFxyXG4gICAgZm9jdXNNb2RlOiBmYWxzZSwgXHJcbiAgICBmb2N1c1Rhc2s6IG51bGwgXHJcbiAgfSksXHJcbiAgXHJcbiAgLy8gUmVzZXQgYWxsIFVJIHN0YXRlXHJcbiAgcmVzZXRVSVN0YXRlOiAoKSA9PiBzZXQoe1xyXG4gICAgc2lkZWJhck9wZW46IHRydWUsXHJcbiAgICB0YXNrTW9kYWxPcGVuOiBmYWxzZSxcclxuICAgIHRhc2tNb2RhbERhdGE6IG51bGwsXHJcbiAgICBhaUFzc2lzdGFudE9wZW46IGZhbHNlLFxyXG4gICAgc2VhcmNoT3BlbjogZmFsc2UsXHJcbiAgICBzZWFyY2hRdWVyeTogJycsXHJcbiAgICBzZWFyY2hSZXN1bHRzOiBbXSxcclxuICAgIGZvY3VzTW9kZTogZmFsc2UsXHJcbiAgICBmb2N1c1Rhc2s6IG51bGwsXHJcbiAgICBub3RpZmljYXRpb25zOiBbXVxyXG4gIH0pXHJcbn0pKTsiXSwibmFtZXMiOlsiY3JlYXRlIiwidXNlVUlTdG9yZSIsInNldCIsImdldCIsInNpZGViYXJPcGVuIiwidGFza01vZGFsT3BlbiIsInRhc2tNb2RhbERhdGEiLCJhaUFzc2lzdGFudE9wZW4iLCJjYWxlbmRhclZpZXciLCJzZWxlY3RlZERhdGUiLCJEYXRlIiwidGhlbWUiLCJjb21wYWN0TW9kZSIsIm5vdGlmaWNhdGlvbnMiLCJnbG9iYWxMb2FkaW5nIiwidG9nZ2xlU2lkZWJhciIsInN0YXRlIiwic2V0U2lkZWJhck9wZW4iLCJvcGVuIiwib3BlblRhc2tNb2RhbCIsInRhc2tEYXRhIiwiY2xvc2VUYXNrTW9kYWwiLCJ0b2dnbGVBSUFzc2lzdGFudCIsInNldEFJQXNzaXN0YW50T3BlbiIsInNldENhbGVuZGFyVmlldyIsInZpZXciLCJzZXRTZWxlY3RlZERhdGUiLCJkYXRlIiwic2V0VGhlbWUiLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsImNsYXNzTGlzdCIsImFkZCIsInJlbW92ZSIsInRvZ2dsZUNvbXBhY3RNb2RlIiwiYWRkTm90aWZpY2F0aW9uIiwibm90aWZpY2F0aW9uIiwiaWQiLCJub3ciLCJ0b1N0cmluZyIsIm5ld05vdGlmaWNhdGlvbiIsInR5cGUiLCJ0aXRsZSIsIm1lc3NhZ2UiLCJkdXJhdGlvbiIsInNldFRpbWVvdXQiLCJyZW1vdmVOb3RpZmljYXRpb24iLCJmaWx0ZXIiLCJuIiwiY2xlYXJOb3RpZmljYXRpb25zIiwic2V0R2xvYmFsTG9hZGluZyIsImxvYWRpbmciLCJzaG9ydGN1dHNFbmFibGVkIiwidG9nZ2xlU2hvcnRjdXRzIiwic2VhcmNoUXVlcnkiLCJzZWFyY2hSZXN1bHRzIiwic2VhcmNoT3BlbiIsInNldFNlYXJjaFF1ZXJ5IiwicXVlcnkiLCJzZXRTZWFyY2hSZXN1bHRzIiwicmVzdWx0cyIsInRvZ2dsZVNlYXJjaCIsInZpZXdQcmVmZXJlbmNlcyIsImthbmJhbiIsImNvbHVtbnNWaXNpYmxlIiwiY29tcGFjdENhcmRzIiwidG9kbyIsInNob3dDb21wbGV0ZWQiLCJncm91cEJ5IiwiY2FsZW5kYXIiLCJzaG93V2Vla2VuZHMiLCJzdGFydEhvdXIiLCJlbmRIb3VyIiwidXBkYXRlVmlld1ByZWZlcmVuY2UiLCJrZXkiLCJ2YWx1ZSIsImRyYWdTdGF0ZSIsImlzRHJhZ2dpbmciLCJkcmFnZ2VkSXRlbSIsImRyb3Bab25lIiwic2V0RHJhZ1N0YXRlIiwiZm9jdXNNb2RlIiwiZm9jdXNUYXNrIiwiZW50ZXJGb2N1c01vZGUiLCJ0YXNrIiwiZXhpdEZvY3VzTW9kZSIsInJlc2V0VUlTdGF0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./store/uiStore.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "zustand":
/*!**************************!*\
  !*** external "zustand" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();