// Zustand store for task management state
import { create } from 'zustand';
import { 
  collection, 
  doc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy 
} from 'firebase/firestore';
import { db, COLLECTIONS } from '../lib/firebase-config';

export const useTaskStore = create((set, get) => ({
  // State
  tasks: [],
  isLoading: false,
  error: null,
  filter: 'all', // all, active, completed
  sortBy: 'createdAt', // createdAt, dueDate, priority, title

  // Actions
  setTasks: (tasks) => set({ tasks }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  setFilter: (filter) => set({ filter }),
  setSortBy: (sortBy) => set({ sortBy }),

  // Fetch tasks from Firestore
  fetchTasks: async (userId) => {
    set({ isLoading: true, error: null });
    
    try {
      const tasksRef = collection(db, COLLECTIONS.TASKS);
      const q = query(
        tasksRef,
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const tasks = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      set({ tasks, isLoading: false });
    } catch (error) {
      console.error('Error fetching tasks:', error);
      set({ error: error.message, isLoading: false });
    }
  },

  // Create new task
  createTask: async (taskData) => {
    set({ isLoading: true, error: null });
    
    try {
      const tasksRef = collection(db, COLLECTIONS.TASKS);
      const docRef = await addDoc(tasksRef, {
        ...taskData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      const newTask = { id: docRef.id, ...taskData };
      const currentTasks = get().tasks;
      
      set({ 
        tasks: [newTask, ...currentTasks],
        isLoading: false 
      });
      
      return newTask;
    } catch (error) {
      console.error('Error creating task:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Update existing task
  updateTask: async (taskId, updates) => {
    set({ isLoading: true, error: null });
    
    try {
      const taskRef = doc(db, COLLECTIONS.TASKS, taskId);
      await updateDoc(taskRef, {
        ...updates,
        updatedAt: new Date().toISOString()
      });
      
      const currentTasks = get().tasks;
      const updatedTasks = currentTasks.map(task =>
        task.id === taskId ? { ...task, ...updates } : task
      );
      
      set({ 
        tasks: updatedTasks,
        isLoading: false 
      });
    } catch (error) {
      console.error('Error updating task:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Delete task
  deleteTask: async (taskId) => {
    set({ isLoading: true, error: null });
    
    try {
      const taskRef = doc(db, COLLECTIONS.TASKS, taskId);
      await deleteDoc(taskRef);
      
      const currentTasks = get().tasks;
      const filteredTasks = currentTasks.filter(task => task.id !== taskId);
      
      set({ 
        tasks: filteredTasks,
        isLoading: false 
      });
    } catch (error) {
      console.error('Error deleting task:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Toggle task completion
  toggleTaskCompletion: async (taskId) => {
    const task = get().tasks.find(t => t.id === taskId);
    if (!task) return;
    
    const newStatus = task.status === 'done' ? 'todo' : 'done';
    await get().updateTask(taskId, { status: newStatus });
  },

  // Bulk operations
  bulkUpdateTasks: async (taskIds, updates) => {
    set({ isLoading: true, error: null });
    
    try {
      const promises = taskIds.map(taskId => {
        const taskRef = doc(db, COLLECTIONS.TASKS, taskId);
        return updateDoc(taskRef, {
          ...updates,
          updatedAt: new Date().toISOString()
        });
      });
      
      await Promise.all(promises);
      
      const currentTasks = get().tasks;
      const updatedTasks = currentTasks.map(task =>
        taskIds.includes(task.id) ? { ...task, ...updates } : task
      );
      
      set({ 
        tasks: updatedTasks,
        isLoading: false 
      });
    } catch (error) {
      console.error('Error bulk updating tasks:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Computed getters
  getFilteredTasks: () => {
    const { tasks, filter, sortBy } = get();
    
    // Apply filter
    let filteredTasks = tasks;
    switch (filter) {
      case 'active':
        filteredTasks = tasks.filter(task => task.status !== 'done');
        break;
      case 'completed':
        filteredTasks = tasks.filter(task => task.status === 'done');
        break;
      default:
        filteredTasks = tasks;
    }
    
    // Apply sorting
    return filteredTasks.sort((a, b) => {
      switch (sortBy) {
        case 'dueDate':
          if (!a.dueDate && !b.dueDate) return 0;
          if (!a.dueDate) return 1;
          if (!b.dueDate) return -1;
          return new Date(a.dueDate) - new Date(b.dueDate);
        
        case 'priority':
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
        
        case 'title':
          return a.title.localeCompare(b.title);
        
        default: // createdAt
          return new Date(b.createdAt) - new Date(a.createdAt);
      }
    });
  },

  // Analytics helpers
  getTaskStats: () => {
    const tasks = get().tasks;
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    return {
      total: tasks.length,
      completed: tasks.filter(t => t.status === 'done').length,
      active: tasks.filter(t => t.status !== 'done').length,
      overdue: tasks.filter(t => 
        t.dueDate && new Date(t.dueDate) < today && t.status !== 'done'
      ).length,
      dueToday: tasks.filter(t => 
        t.dueDate && 
        new Date(t.dueDate).toDateString() === today.toDateString() &&
        t.status !== 'done'
      ).length,
      completedToday: tasks.filter(t => 
        t.status === 'done' &&
        t.updatedAt &&
        new Date(t.updatedAt).toDateString() === today.toDateString()
      ).length
    };
  }
}));