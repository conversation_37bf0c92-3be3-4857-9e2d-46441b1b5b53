// Data export utilities for CSV and PDF generation
import jsPDF from 'jspdf';
import 'jspdf-autotable';

// Export tasks to CSV format
export const exportTasksToCSV = (tasks, filename = 'focusflow-tasks') => {
  // Define CSV headers
  const headers = [
    'Title',
    'Description',
    'Status',
    'Priority',
    'Category',
    'Due Date',
    'Created Date',
    'Updated Date',
    'Tags'
  ];

  // Convert tasks to CSV rows
  const csvRows = tasks.map(task => [
    task.title || '',
    task.description || '',
    task.status || '',
    task.priority || '',
    task.category || '',
    task.dueDate ? new Date(task.dueDate).toLocaleDateString() : '',
    task.createdAt ? new Date(task.createdAt).toLocaleDateString() : '',
    task.updatedAt ? new Date(task.updatedAt).toLocaleDateString() : '',
    task.tags ? task.tags.join('; ') : ''
  ]);

  // Combine headers and data
  const csvContent = [headers, ...csvRows]
    .map(row => row.map(field => `"${field.toString().replace(/"/g, '""')}"`).join(','))
    .join('\n');

  // Create and download file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// Export tasks to PDF format
export const exportTasksToPDF = (tasks, options = {}) => {
  const {
    filename = 'focusflow-tasks',
    title = 'FocusFlow AI - Task Report',
    includeStats = true,
    groupByStatus = true
  } = options;

  // Create new PDF document
  const doc = new jsPDF();
  let yPosition = 20;

  // Add title
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text(title, 20, yPosition);
  yPosition += 15;

  // Add generation date
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, yPosition);
  yPosition += 20;

  // Add statistics if requested
  if (includeStats) {
    const stats = calculateTaskStats(tasks);
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Task Statistics', 20, yPosition);
    yPosition += 10;

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    const statsText = [
      `Total Tasks: ${stats.total}`,
      `Completed: ${stats.completed}`,
      `In Progress: ${stats.inProgress}`,
      `Pending: ${stats.pending}`,
      `Overdue: ${stats.overdue}`
    ];

    statsText.forEach(stat => {
      doc.text(stat, 20, yPosition);
      yPosition += 6;
    });
    yPosition += 10;
  }

  // Group tasks by status if requested
  if (groupByStatus) {
    const groupedTasks = groupTasksByStatus(tasks);
    
    Object.entries(groupedTasks).forEach(([status, statusTasks]) => {
      if (statusTasks.length === 0) return;
      
      // Check if we need a new page
      if (yPosition > 250) {
        doc.addPage();
        yPosition = 20;
      }

      // Status header
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text(`${status.toUpperCase()} (${statusTasks.length})`, 20, yPosition);
      yPosition += 10;

      // Tasks table
      const tableData = statusTasks.map(task => [
        task.title,
        task.priority || 'N/A',
        task.category || 'N/A',
        task.dueDate ? new Date(task.dueDate).toLocaleDateString() : 'No due date'
      ]);

      doc.autoTable({
        head: [['Task', 'Priority', 'Category', 'Due Date']],
        body: tableData,
        startY: yPosition,
        styles: { fontSize: 8 },
        headStyles: { fillColor: [66, 139, 202] },
        margin: { left: 20, right: 20 }
      });

      yPosition = doc.lastAutoTable.finalY + 15;
    });
  } else {
    // Single table with all tasks
    const tableData = tasks.map(task => [
      task.title,
      task.status || 'N/A',
      task.priority || 'N/A',
      task.category || 'N/A',
      task.dueDate ? new Date(task.dueDate).toLocaleDateString() : 'No due date'
    ]);

    doc.autoTable({
      head: [['Task', 'Status', 'Priority', 'Category', 'Due Date']],
      body: tableData,
      startY: yPosition,
      styles: { fontSize: 8 },
      headStyles: { fillColor: [66, 139, 202] },
      margin: { left: 20, right: 20 }
    });
  }

  // Save the PDF
  doc.save(`${filename}.pdf`);
};

// Export productivity analytics to PDF
export const exportAnalyticsToPDF = (analytics, tasks, filename = 'focusflow-analytics') => {
  const doc = new jsPDF();
  let yPosition = 20;

  // Title
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text('FocusFlow AI - Productivity Analytics', 20, yPosition);
  yPosition += 15;

  // Date range
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text(`Report Period: ${analytics.dateRange || 'All Time'}`, 20, yPosition);
  yPosition += 20;

  // Key Metrics
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('Key Metrics', 20, yPosition);
  yPosition += 10;

  const metrics = [
    ['Total Tasks Created', analytics.totalTasks || 0],
    ['Tasks Completed', analytics.completedTasks || 0],
    ['Completion Rate', `${analytics.completionRate || 0}%`],
    ['Average Daily Tasks', analytics.avgDailyTasks || 0],
    ['Focus Score', `${analytics.focusScore || 0}%`]
  ];

  doc.autoTable({
    body: metrics,
    startY: yPosition,
    styles: { fontSize: 10 },
    columnStyles: {
      0: { fontStyle: 'bold', cellWidth: 80 },
      1: { cellWidth: 40 }
    },
    margin: { left: 20, right: 20 }
  });

  yPosition = doc.lastAutoTable.finalY + 20;

  // Task Distribution by Category
  if (analytics.categoryDistribution) {
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Task Distribution by Category', 20, yPosition);
    yPosition += 10;

    const categoryData = Object.entries(analytics.categoryDistribution).map(([category, count]) => [
      category.charAt(0).toUpperCase() + category.slice(1),
      count,
      `${((count / analytics.totalTasks) * 100).toFixed(1)}%`
    ]);

    doc.autoTable({
      head: [['Category', 'Count', 'Percentage']],
      body: categoryData,
      startY: yPosition,
      styles: { fontSize: 10 },
      headStyles: { fillColor: [66, 139, 202] },
      margin: { left: 20, right: 20 }
    });
  }

  doc.save(`${filename}.pdf`);
};

// Helper function to calculate task statistics
const calculateTaskStats = (tasks) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  return {
    total: tasks.length,
    completed: tasks.filter(t => t.status === 'done').length,
    inProgress: tasks.filter(t => t.status === 'in-progress').length,
    pending: tasks.filter(t => t.status === 'todo').length,
    overdue: tasks.filter(t => 
      t.dueDate && 
      new Date(t.dueDate) < today && 
      t.status !== 'done'
    ).length
  };
};

// Helper function to group tasks by status
const groupTasksByStatus = (tasks) => {
  return tasks.reduce((groups, task) => {
    const status = task.status || 'todo';
    if (!groups[status]) {
      groups[status] = [];
    }
    groups[status].push(task);
    return groups;
  }, {});
};

// Export calendar events to ICS format
export const exportCalendarToICS = (tasks, filename = 'focusflow-calendar') => {
  const icsContent = generateICSContent(tasks);
  
  const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.ics`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// Generate ICS calendar content
const generateICSContent = (tasks) => {
  const now = new Date();
  const timestamp = now.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
  
  let icsContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//FocusFlow AI//Task Calendar//EN',
    'CALSCALE:GREGORIAN'
  ];

  tasks.forEach(task => {
    if (task.dueDate) {
      const dueDate = new Date(task.dueDate);
      const dueDateStr = dueDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
      
      icsContent.push(
        'BEGIN:VEVENT',
        `UID:${task.id}@focusflow.ai`,
        `DTSTAMP:${timestamp}`,
        `DTSTART:${dueDateStr}`,
        `SUMMARY:${task.title}`,
        `DESCRIPTION:${task.description || ''}`,
        `CATEGORIES:${task.category || 'Task'}`,
        `PRIORITY:${task.priority === 'high' ? '1' : task.priority === 'medium' ? '5' : '9'}`,
        'END:VEVENT'
      );
    }
  });

  icsContent.push('END:VCALENDAR');
  
  return icsContent.join('\r\n');
};