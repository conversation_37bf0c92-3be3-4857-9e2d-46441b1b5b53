// Firebase configuration and initialization
import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

// Firebase configuration object from environment variables
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Initialize Firebase app
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Connect to emulators in development
if (process.env.NODE_ENV === 'development') {
  try {
    // Connect to Auth emulator
    connectAuthEmulator(auth, 'http://localhost:9099', { disableWarnings: true });
    
    // Connect to Firestore emulator
    connectFirestoreEmulator(db, 'localhost', 8080);
  } catch (error) {
    // Emulators already connected or not available
    console.log('Firebase emulators connection skipped:', error.message);
  }
}

// Firestore collections structure
export const COLLECTIONS = {
  USERS: 'users',
  TASKS: 'tasks',
  PROJECTS: 'projects',
  AI_CONVERSATIONS: 'ai_conversations',
  ANALYTICS: 'analytics'
};

// Security rules helper - ensures user can only access their own data
export const getUserDocRef = (collection, userId) => {
  return `${collection}/${userId}`;
};

// END-TO-END ENCRYPTION: Data encryption utilities for sensitive information
export const encryptSensitiveData = (data) => {
  // Implementation would use crypto-js or similar for client-side encryption
  // This ensures sensitive task data is encrypted before storing in Firestore
  return data; // Placeholder - implement actual encryption
};

export const decryptSensitiveData = (encryptedData) => {
  // Decrypt data when retrieving from Firestore
  return encryptedData; // Placeholder - implement actual decryption
};

export default app;