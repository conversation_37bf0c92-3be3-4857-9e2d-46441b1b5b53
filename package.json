{"name": "focusflow-ai", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@heroicons/react": "^2.2.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.14", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "firebase": "^11.10.0", "jspdf": "^3.0.1", "jspdf-autotable": "^3.6.0", "json2csv": "^6.0.0-alpha.2", "lucide-react": "^0.525.0", "moment": "^2.30.1", "next": "^14.0.0", "react": "^18.3.1", "react-big-calendar": "^1.19.4", "react-dom": "^18.3.1", "react-firebase-hooks": "^5.1.1", "react-hot-toast": "^2.5.2", "react-speech-recognition": "^3.10.0", "regenerator-runtime": "^0.14.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-config-next": "^14.0.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}