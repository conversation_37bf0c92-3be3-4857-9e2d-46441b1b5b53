// Individual chat message component for AI assistant
import { useState } from 'react';

export default function ChatMessage({ message }) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  const isLongMessage = message.content.length > 200;

  return (
    <div className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
        message.isUser
          ? 'bg-blue-600 text-white'
          : message.isError
          ? 'bg-red-100 text-red-800 border border-red-200'
          : 'bg-gray-100 text-gray-900'
      }`}>
        {/* Message Content */}
        <div className="text-sm">
          {isLongMessage && !isExpanded ? (
            <>
              {message.content.substring(0, 200)}...
              <button
                onClick={() => setIsExpanded(true)}
                className={`ml-2 underline ${
                  message.isUser ? 'text-blue-200' : 'text-blue-600'
                }`}
              >
                Show more
              </button>
            </>
          ) : (
            <>
              {message.content}
              {isLongMessage && isExpanded && (
                <button
                  onClick={() => setIsExpanded(false)}
                  className={`ml-2 underline ${
                    message.isUser ? 'text-blue-200' : 'text-blue-600'
                  }`}
                >
                  Show less
                </button>
              )}
            </>
          )}
        </div>

        {/* Action Indicator */}
        {message.actionPerformed && (
          <div className="mt-2 text-xs opacity-75">
            <span className="inline-flex items-center space-x-1">
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span>Action: {message.actionPerformed}</span>
            </span>
          </div>
        )}

        {/* Timestamp */}
        <div className={`text-xs mt-1 ${
          message.isUser ? 'text-blue-200' : 'text-gray-500'
        }`}>
          {formatTimestamp(message.timestamp)}
        </div>
      </div>
    </div>
  );
}