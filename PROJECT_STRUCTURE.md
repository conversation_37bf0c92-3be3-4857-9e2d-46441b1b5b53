# FocusFlow AI - Project Structure

## 📁 Directory Structure

```
focusflow-ai/
├── .env.local                    # Environment variables (Firebase, OpenRouter)
├── .env.local.example           # Template for environment setup
├── next.config.js               # Next.js configuration
├── package.json                 # Dependencies and scripts
├── README.md                    # Project documentation
│
├── components/                  # Reusable UI components
│   ├── ui/                     # Base UI components (shadcn/ui style)
│   │   ├── Button.js           # Reusable button component
│   │   ├── Card.js             # Card container component
│   │   ├── Input.js            # Form input component
│   │   └── Modal.js            # Modal dialog component
│   │
│   ├── layout/                 # Layout components
│   │   ├── Sidebar.js          # Navigation sidebar
│   │   ├── Header.js           # Top header with user info
│   │   └── Layout.js           # Main layout wrapper
│   │
│   ├── tasks/                  # Task management components
│   │   ├── KanbanBoard.js      # Drag-drop Kanban interface
│   │   ├── TodoList.js         # Simple todo list view
│   │   ├── TaskCard.js         # Individual task component
│   │   └── TaskForm.js         # Create/edit task form
│   │
│   ├── ai/                     # AI assistant components
│   │   ├── AIAssistant.js      # Main AI chat interface
│   │   ├── ChatMessage.js      # Individual chat message
│   │   └── CommandProcessor.js # Process AI commands
│   │
│   ├── calendar/               # Calendar components
│   │   ├── CalendarView.js     # Main calendar interface
│   │   └── EventModal.js       # Event creation/editing
│   │
│   └── analytics/              # Analytics and reporting
│       ├── Dashboard.js        # Analytics dashboard
│       └── ExportTools.js      # CSV/PDF export controls
│
├── lib/                        # Core configuration and utilities
│   ├── firebase-config.js      # Firebase setup and auth
│   ├── openrouter-config.js    # OpenRouter AI API setup
│   ├── encryption.js           # End-to-end encryption utilities
│   └── constants.js            # App constants and enums
│
├── pages/                      # Next.js pages
│   ├── api/                    # API routes
│   │   ├── auth/               # Authentication endpoints
│   │   ├── tasks/              # Task CRUD operations
│   │   ├── ai/                 # AI assistant endpoints
│   │   └── export/             # Data export endpoints
│   │
│   ├── _app.js                 # App wrapper with providers
│   ├── index.js                # Main dashboard page
│   ├── login.js                # Authentication page
│   └── settings.js             # User settings page
│
├── store/                      # State management (Zustand)
│   ├── authStore.js            # Authentication state
│   ├── taskStore.js            # Task management state
│   ├── aiStore.js              # AI assistant state
│   └── uiStore.js              # UI state (modals, sidebar)
│
├── styles/                     # Styling
│   └── globals.css             # Global styles with Tailwind
│
└── utils/                      # Utility functions
    ├── exportData.js           # CSV/PDF export utilities
    ├── dateHelpers.js          # Date formatting and manipulation
    ├── taskHelpers.js          # Task-related utility functions
    └── voiceCommands.js        # Voice recognition utilities
```

## 🏗️ Implementation Phases

### Phase 1: Foundation (Auth + Database + Security)
- Firebase authentication and Firestore setup
- End-to-end encryption for sensitive data
- Basic user management and data structure

### Phase 2: Core UI (Layout + Task Management)
- Responsive layout with sidebar navigation
- Kanban board with drag-and-drop functionality
- Todo list view and task CRUD operations

### Phase 3: AI Integration (OpenRouter + Command Processing)
- AI assistant chat interface
- Natural language task creation and management
- Multi-model AI API integration

### Phase 4: Advanced Features (Calendar + Analytics + Export)
- Calendar view with task scheduling
- Time tracking and productivity analytics
- Data export (CSV/PDF) and voice commands