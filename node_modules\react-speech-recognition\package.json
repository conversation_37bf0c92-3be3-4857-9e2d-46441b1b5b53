{"name": "react-speech-recognition", "version": "3.10.0", "description": "💬Speech recognition for your React app", "main": "lib/index.js", "homepage": "https://webspeechrecognition.com/", "scripts": {"build": "babel src -d lib --ignore '**/*.test.js'", "lint": "eslint src tests", "test": "jest --testPathIgnorePatterns=example", "coverage": "npm run coverage:collect && npm run coverage:report", "coverage:collect": "jest --testPathIgnorePatterns=example --collectCoverageFrom=src/**/*.{js,jsx} --coverage", "coverage:report": "open coverage/lcov-report/index.html", "prepublishOnly": "npm run build", "update-example": "cp -R src/ example/src/SpeechRecognition", "run-example": "cd example && npm i && npm start", "dev": "npm run watch & npm run update-example && npm run run-example", "watch": "npm-watch update-example", "bump": "npm version patch -m \"v%s\" && git push --tags"}, "watch": {"update-example": "src/*.js"}, "repository": {"type": "git", "url": "https://github.com/JamesBrill/react-speech-recognition.git"}, "keywords": ["react", "reactjs", "speech", "recognition"], "files": ["lib"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "peerDependencies": {"react": ">=16.8.0"}, "devDependencies": {"@babel/cli": "^7.10.3", "@babel/core": "^7.10.3", "@babel/preset-env": "^7.10.3", "@babel/preset-react": "^7.10.1", "@testing-library/react-hooks": "^3.3.0", "babel-eslint": "^10.0.2", "eslint": "^6.0.1", "eslint-config-standard": "^13.0.1", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.18.0", "eslint-plugin-jest": "^23.17.1", "eslint-plugin-node": "^9.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "^7.14.2", "eslint-plugin-standard": "^4.0.0", "jest": "^26.1.0", "npm-watch": "^0.6.0", "react": "^16.13.1", "react-dom": "^16.13.1", "react-test-renderer": "16.13.1"}, "jest": {"coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}}