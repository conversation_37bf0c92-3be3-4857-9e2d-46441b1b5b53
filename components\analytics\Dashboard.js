// Analytics dashboard component
import { useMemo } from 'react';
import { exportTasksToCSV, exportTasksToPDF } from '../../utils/exportData';

export default function Dashboard({ tasks, userId }) {
  // Calculate analytics data
  const analytics = useMemo(() => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const stats = {
      total: tasks.length,
      completed: tasks.filter(t => t.status === 'done').length,
      active: tasks.filter(t => t.status !== 'done').length,
      overdue: tasks.filter(t => 
        t.dueDate && new Date(t.dueDate) < today && t.status !== 'done'
      ).length,
      completedToday: tasks.filter(t => 
        t.status === 'done' &&
        t.updatedAt &&
        new Date(t.updatedAt) >= today
      ).length,
      completedThisWeek: tasks.filter(t => 
        t.status === 'done' &&
        t.updatedAt &&
        new Date(t.updatedAt) >= thisWeek
      ).length,
      completedThisMonth: tasks.filter(t => 
        t.status === 'done' &&
        t.updatedAt &&
        new Date(t.updatedAt) >= thisMonth
      ).length
    };

    // Category distribution
    const categoryDistribution = tasks.reduce((acc, task) => {
      const category = task.category || 'uncategorized';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    // Priority distribution
    const priorityDistribution = tasks.reduce((acc, task) => {
      const priority = task.priority || 'none';
      acc[priority] = (acc[priority] || 0) + 1;
      return acc;
    }, {});

    // Completion rate
    const completionRate = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;

    // Focus score (based on completion rate and overdue tasks)
    const focusScore = Math.max(0, completionRate - (stats.overdue * 5));

    return {
      ...stats,
      categoryDistribution,
      priorityDistribution,
      completionRate,
      focusScore
    };
  }, [tasks]);

  const handleExportCSV = () => {
    exportTasksToCSV(tasks, `focusflow-tasks-${new Date().toISOString().split('T')[0]}`);
  };

  const handleExportPDF = () => {
    exportTasksToPDF(tasks, {
      filename: `focusflow-report-${new Date().toISOString().split('T')[0]}`,
      title: 'FocusFlow AI - Productivity Report',
      includeStats: true,
      groupByStatus: true
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900">Analytics Dashboard</h2>
          <p className="text-gray-600">Track your productivity and insights</p>
        </div>
        
        {/* Export buttons */}
        <div className="flex space-x-2">
          <button
            onClick={handleExportCSV}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span>Export CSV</span>
          </button>
          <button
            onClick={handleExportPDF}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span>Export PDF</span>
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Tasks"
          value={analytics.total}
          icon="📋"
          color="bg-blue-500"
        />
        <MetricCard
          title="Completed"
          value={analytics.completed}
          icon="✅"
          color="bg-green-500"
        />
        <MetricCard
          title="Active Tasks"
          value={analytics.active}
          icon="⏳"
          color="bg-yellow-500"
        />
        <MetricCard
          title="Overdue"
          value={analytics.overdue}
          icon="⚠️"
          color="bg-red-500"
        />
      </div>

      {/* Progress Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Completion Rate */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Completion Rate</h3>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-blue-600 h-3 rounded-full transition-all duration-500"
                  style={{ width: `${analytics.completionRate}%` }}
                />
              </div>
            </div>
            <span className="text-2xl font-bold text-gray-900">
              {analytics.completionRate}%
            </span>
          </div>
        </div>

        {/* Focus Score */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Focus Score</h3>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className={`h-3 rounded-full transition-all duration-500 ${
                    analytics.focusScore >= 80 ? 'bg-green-500' :
                    analytics.focusScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${Math.min(100, analytics.focusScore)}%` }}
                />
              </div>
            </div>
            <span className="text-2xl font-bold text-gray-900">
              {analytics.focusScore}%
            </span>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Today</h3>
          <div className="text-3xl font-bold text-green-600 mb-2">
            {analytics.completedToday}
          </div>
          <p className="text-gray-600">Tasks completed</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">This Week</h3>
          <div className="text-3xl font-bold text-blue-600 mb-2">
            {analytics.completedThisWeek}
          </div>
          <p className="text-gray-600">Tasks completed</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">This Month</h3>
          <div className="text-3xl font-bold text-purple-600 mb-2">
            {analytics.completedThisMonth}
          </div>
          <p className="text-gray-600">Tasks completed</p>
        </div>
      </div>

      {/* Distribution Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Category Distribution */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Tasks by Category</h3>
          <div className="space-y-3">
            {Object.entries(analytics.categoryDistribution).map(([category, count]) => (
              <div key={category} className="flex items-center justify-between">
                <span className="capitalize text-gray-700">{category}</span>
                <div className="flex items-center space-x-2">
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${(count / analytics.total) * 100}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-8 text-right">
                    {count}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Priority Distribution */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Tasks by Priority</h3>
          <div className="space-y-3">
            {Object.entries(analytics.priorityDistribution).map(([priority, count]) => {
              const colors = {
                high: 'bg-red-500',
                medium: 'bg-yellow-500',
                low: 'bg-green-500',
                none: 'bg-gray-400'
              };
              
              return (
                <div key={priority} className="flex items-center justify-between">
                  <span className="capitalize text-gray-700">
                    {priority === 'none' ? 'No priority' : priority}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${colors[priority]}`}
                        style={{ width: `${(count / analytics.total) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-8 text-right">
                      {count}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

// Metric card component
function MetricCard({ title, value, icon, color }) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center">
        <div className={`${color} p-3 rounded-lg text-white text-xl mr-4`}>
          {icon}
        </div>
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
      </div>
    </div>
  );
}