// Zustand store for AI assistant state management
import { create } from 'zustand';

export const useAIStore = create((set, get) => ({
  // Chat messages
  messages: [],
  
  // AI state
  isProcessing: false,
  currentModel: 'anthropic/claude-3-haiku',
  
  // Conversation context
  conversationId: null,
  context: {},
  
  // AI capabilities
  capabilities: {
    taskManagement: true,
    scheduling: true,
    analytics: true,
    voiceCommands: false,
    fileAnalysis: false
  },
  
  // User preferences for AI
  preferences: {
    responseStyle: 'concise', // concise, detailed, friendly
    autoExecuteCommands: false,
    confirmActions: true,
    saveConversations: true
  },
  
  // Actions
  addMessage: (message) => {
    const newMessage = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      isUser: false,
      ...message
    };
    
    set((state) => ({
      messages: [...state.messages, newMessage]
    }));
    
    // Save to localStorage if enabled
    if (get().preferences.saveConversations) {
      get().saveConversationToStorage();
    }
  },
  
  removeMessage: (messageId) => set((state) => ({
    messages: state.messages.filter(msg => msg.id !== messageId)
  })),
  
  clearMessages: () => {
    set({ messages: [], conversationId: null });
    if (typeof window !== 'undefined') {
      localStorage.removeItem('ai_conversation');
    }
  },
  
  updateMessage: (messageId, updates) => set((state) => ({
    messages: state.messages.map(msg =>
      msg.id === messageId ? { ...msg, ...updates } : msg
    )
  })),
  
  // Processing state
  setProcessing: (isProcessing) => set({ isProcessing }),
  
  // Model management
  setCurrentModel: (model) => set({ currentModel: model }),
  
  // Context management
  updateContext: (newContext) => set((state) => ({
    context: { ...state.context, ...newContext }
  })),
  
  clearContext: () => set({ context: {} }),
  
  // Conversation management
  startNewConversation: () => {
    const conversationId = Date.now().toString();
    set({ 
      conversationId,
      messages: [],
      context: {}
    });
    return conversationId;
  },
  
  // Preferences
  updatePreferences: (newPreferences) => set((state) => ({
    preferences: { ...state.preferences, ...newPreferences }
  })),
  
  // Capabilities
  updateCapabilities: (newCapabilities) => set((state) => ({
    capabilities: { ...state.capabilities, ...newCapabilities }
  })),
  
  // Storage management
  saveConversationToStorage: () => {
    if (typeof window === 'undefined') return;
    
    const { messages, conversationId, context } = get();
    const conversationData = {
      messages: messages.slice(-50), // Keep last 50 messages
      conversationId,
      context,
      savedAt: new Date().toISOString()
    };
    
    try {
      localStorage.setItem('ai_conversation', JSON.stringify(conversationData));
    } catch (error) {
      console.warn('Failed to save conversation to localStorage:', error);
    }
  },
  
  loadConversationFromStorage: () => {
    if (typeof window === 'undefined') return;
    
    try {
      const saved = localStorage.getItem('ai_conversation');
      if (saved) {
        const conversationData = JSON.parse(saved);
        set({
          messages: conversationData.messages || [],
          conversationId: conversationData.conversationId,
          context: conversationData.context || {}
        });
      }
    } catch (error) {
      console.warn('Failed to load conversation from localStorage:', error);
    }
  },
  
  // Command history
  commandHistory: [],
  
  addToCommandHistory: (command) => {
    set((state) => ({
      commandHistory: [
        command,
        ...state.commandHistory.slice(0, 49) // Keep last 50 commands
      ]
    }));
  },
  
  getCommandSuggestions: (input) => {
    const { commandHistory } = get();
    return commandHistory
      .filter(cmd => cmd.toLowerCase().includes(input.toLowerCase()))
      .slice(0, 5);
  },
  
  // Analytics
  analytics: {
    totalMessages: 0,
    commandsExecuted: 0,
    tasksCreated: 0,
    conversationsStarted: 0,
    averageResponseTime: 0
  },
  
  updateAnalytics: (updates) => set((state) => ({
    analytics: { ...state.analytics, ...updates }
  })),
  
  incrementAnalytic: (key) => set((state) => ({
    analytics: {
      ...state.analytics,
      [key]: (state.analytics[key] || 0) + 1
    }
  })),
  
  // Quick actions
  quickActions: [
    {
      id: 'create_task',
      label: 'Create Task',
      command: 'Create a new task',
      icon: 'plus'
    },
    {
      id: 'list_tasks',
      label: 'List Tasks',
      command: 'Show me my active tasks',
      icon: 'list'
    },
    {
      id: 'schedule_meeting',
      label: 'Schedule Meeting',
      command: 'Schedule a meeting',
      icon: 'calendar'
    },
    {
      id: 'productivity_tips',
      label: 'Productivity Tips',
      command: 'Give me some productivity tips',
      icon: 'lightbulb'
    }
  ],
  
  executeQuickAction: (actionId) => {
    const action = get().quickActions.find(a => a.id === actionId);
    if (action) {
      get().addMessage({
        content: action.command,
        isUser: true
      });
      return action.command;
    }
    return null;
  },
  
  // Error handling
  lastError: null,
  
  setError: (error) => set({ lastError: error }),
  clearError: () => set({ lastError: null }),
  
  // Reset store
  reset: () => set({
    messages: [],
    isProcessing: false,
    conversationId: null,
    context: {},
    commandHistory: [],
    lastError: null
  })
}));