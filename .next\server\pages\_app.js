/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_uiStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../store/uiStore */ \"./store/uiStore.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_store_uiStore__WEBPACK_IMPORTED_MODULE_3__]);\n_store_uiStore__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// Main app wrapper with providers and global setup\n\n\n\n\nfunction App({ Component, pageProps }) {\n    const { setTheme } = (0,_store_uiStore__WEBPACK_IMPORTED_MODULE_3__.useUIStore)();\n    // Initialize theme on app load\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Check for saved theme preference or default to 'light'\n        const savedTheme = localStorage.getItem(\"theme\") || \"light\";\n        setTheme(savedTheme);\n    }, [\n        setTheme\n    ]);\n    // Global error boundary\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const handleError = (event)=>{\n            console.error(\"Global error:\", event.error);\n        // You could add error reporting here\n        };\n        const handleUnhandledRejection = (event)=>{\n            console.error(\"Unhandled promise rejection:\", event.reason);\n        // You could add error reporting here\n        };\n        window.addEventListener(\"error\", handleError);\n        window.addEventListener(\"unhandledrejection\", handleUnhandledRejection);\n        return ()=>{\n            window.removeEventListener(\"error\", handleError);\n            window.removeEventListener(\"unhandledrejection\", handleUnhandledRejection);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\_app.js\",\n        lineNumber: 37,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./store/uiStore.js":
/*!**************************!*\
  !*** ./store/uiStore.js ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUIStore: () => (/* binding */ useUIStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"zustand\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zustand__WEBPACK_IMPORTED_MODULE_0__]);\nzustand__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// Zustand store for UI state management\n\nconst useUIStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        // Sidebar state\n        sidebarOpen: true,\n        // Modal states\n        taskModalOpen: false,\n        taskModalData: null,\n        // AI Assistant state\n        aiAssistantOpen: false,\n        // Calendar state\n        calendarView: \"month\",\n        selectedDate: new Date(),\n        // Theme and preferences\n        theme: \"light\",\n        compactMode: false,\n        // Notification state\n        notifications: [],\n        // Loading states\n        globalLoading: false,\n        // Actions\n        toggleSidebar: ()=>set((state)=>({\n                    sidebarOpen: !state.sidebarOpen\n                })),\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        // Task modal actions\n        openTaskModal: (taskData = null)=>set({\n                taskModalOpen: true,\n                taskModalData: taskData\n            }),\n        closeTaskModal: ()=>set({\n                taskModalOpen: false,\n                taskModalData: null\n            }),\n        // AI Assistant actions\n        toggleAIAssistant: ()=>set((state)=>({\n                    aiAssistantOpen: !state.aiAssistantOpen\n                })),\n        setAIAssistantOpen: (open)=>set({\n                aiAssistantOpen: open\n            }),\n        // Calendar actions\n        setCalendarView: (view)=>set({\n                calendarView: view\n            }),\n        setSelectedDate: (date)=>set({\n                selectedDate: date\n            }),\n        // Theme actions\n        setTheme: (theme)=>{\n            set({\n                theme\n            });\n            // Apply theme to document\n            if (false) {}\n        },\n        toggleCompactMode: ()=>set((state)=>({\n                    compactMode: !state.compactMode\n                })),\n        // Notification actions\n        addNotification: (notification)=>{\n            const id = Date.now().toString();\n            const newNotification = {\n                id,\n                type: \"info\",\n                title: \"\",\n                message: \"\",\n                duration: 5000,\n                ...notification\n            };\n            set((state)=>({\n                    notifications: [\n                        ...state.notifications,\n                        newNotification\n                    ]\n                }));\n            // Auto-remove notification after duration\n            if (newNotification.duration > 0) {\n                setTimeout(()=>{\n                    get().removeNotification(id);\n                }, newNotification.duration);\n            }\n            return id;\n        },\n        removeNotification: (id)=>set((state)=>({\n                    notifications: state.notifications.filter((n)=>n.id !== id)\n                })),\n        clearNotifications: ()=>set({\n                notifications: []\n            }),\n        // Global loading\n        setGlobalLoading: (loading)=>set({\n                globalLoading: loading\n            }),\n        // Keyboard shortcuts state\n        shortcutsEnabled: true,\n        toggleShortcuts: ()=>set((state)=>({\n                    shortcutsEnabled: !state.shortcutsEnabled\n                })),\n        // Search state\n        searchQuery: \"\",\n        searchResults: [],\n        searchOpen: false,\n        setSearchQuery: (query)=>set({\n                searchQuery: query\n            }),\n        setSearchResults: (results)=>set({\n                searchResults: results\n            }),\n        toggleSearch: ()=>set((state)=>({\n                    searchOpen: !state.searchOpen\n                })),\n        // View preferences\n        viewPreferences: {\n            kanban: {\n                columnsVisible: [\n                    \"todo\",\n                    \"in-progress\",\n                    \"review\",\n                    \"done\"\n                ],\n                compactCards: false\n            },\n            todo: {\n                showCompleted: false,\n                groupBy: \"none\" // none, priority, category, dueDate\n            },\n            calendar: {\n                showWeekends: true,\n                startHour: 8,\n                endHour: 18\n            }\n        },\n        updateViewPreference: (view, key, value)=>set((state)=>({\n                    viewPreferences: {\n                        ...state.viewPreferences,\n                        [view]: {\n                            ...state.viewPreferences[view],\n                            [key]: value\n                        }\n                    }\n                })),\n        // Drag and drop state\n        dragState: {\n            isDragging: false,\n            draggedItem: null,\n            dropZone: null\n        },\n        setDragState: (dragState)=>set({\n                dragState\n            }),\n        // Focus mode\n        focusMode: false,\n        focusTask: null,\n        enterFocusMode: (task = null)=>set({\n                focusMode: true,\n                focusTask: task,\n                sidebarOpen: false,\n                aiAssistantOpen: false\n            }),\n        exitFocusMode: ()=>set({\n                focusMode: false,\n                focusTask: null\n            }),\n        // Reset all UI state\n        resetUIState: ()=>set({\n                sidebarOpen: true,\n                taskModalOpen: false,\n                taskModalData: null,\n                aiAssistantOpen: false,\n                searchOpen: false,\n                searchQuery: \"\",\n                searchResults: [],\n                focusMode: false,\n                focusTask: null,\n                notifications: []\n            })\n    }));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zdG9yZS91aVN0b3JlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUEsd0NBQXdDO0FBQ1A7QUFFMUIsTUFBTUMsYUFBYUQsK0NBQU1BLENBQUMsQ0FBQ0UsS0FBS0MsTUFBUztRQUM5QyxnQkFBZ0I7UUFDaEJDLGFBQWE7UUFFYixlQUFlO1FBQ2ZDLGVBQWU7UUFDZkMsZUFBZTtRQUVmLHFCQUFxQjtRQUNyQkMsaUJBQWlCO1FBRWpCLGlCQUFpQjtRQUNqQkMsY0FBYztRQUNkQyxjQUFjLElBQUlDO1FBRWxCLHdCQUF3QjtRQUN4QkMsT0FBTztRQUNQQyxhQUFhO1FBRWIscUJBQXFCO1FBQ3JCQyxlQUFlLEVBQUU7UUFFakIsaUJBQWlCO1FBQ2pCQyxlQUFlO1FBRWYsVUFBVTtRQUNWQyxlQUFlLElBQU1iLElBQUksQ0FBQ2MsUUFBVztvQkFBRVosYUFBYSxDQUFDWSxNQUFNWixXQUFXO2dCQUFDO1FBQ3ZFYSxnQkFBZ0IsQ0FBQ0MsT0FBU2hCLElBQUk7Z0JBQUVFLGFBQWFjO1lBQUs7UUFFbEQscUJBQXFCO1FBQ3JCQyxlQUFlLENBQUNDLFdBQVcsSUFBSSxHQUFLbEIsSUFBSTtnQkFDdENHLGVBQWU7Z0JBQ2ZDLGVBQWVjO1lBQ2pCO1FBQ0FDLGdCQUFnQixJQUFNbkIsSUFBSTtnQkFDeEJHLGVBQWU7Z0JBQ2ZDLGVBQWU7WUFDakI7UUFFQSx1QkFBdUI7UUFDdkJnQixtQkFBbUIsSUFBTXBCLElBQUksQ0FBQ2MsUUFBVztvQkFDdkNULGlCQUFpQixDQUFDUyxNQUFNVCxlQUFlO2dCQUN6QztRQUNBZ0Isb0JBQW9CLENBQUNMLE9BQVNoQixJQUFJO2dCQUFFSyxpQkFBaUJXO1lBQUs7UUFFMUQsbUJBQW1CO1FBQ25CTSxpQkFBaUIsQ0FBQ0MsT0FBU3ZCLElBQUk7Z0JBQUVNLGNBQWNpQjtZQUFLO1FBQ3BEQyxpQkFBaUIsQ0FBQ0MsT0FBU3pCLElBQUk7Z0JBQUVPLGNBQWNrQjtZQUFLO1FBRXBELGdCQUFnQjtRQUNoQkMsVUFBVSxDQUFDakI7WUFDVFQsSUFBSTtnQkFBRVM7WUFBTTtZQUNaLDBCQUEwQjtZQUMxQixJQUFJLEtBQWtCLEVBQWEsRUFNbEM7UUFDSDtRQUVBdUIsbUJBQW1CLElBQU1oQyxJQUFJLENBQUNjLFFBQVc7b0JBQUVKLGFBQWEsQ0FBQ0ksTUFBTUosV0FBVztnQkFBQztRQUUzRSx1QkFBdUI7UUFDdkJ1QixpQkFBaUIsQ0FBQ0M7WUFDaEIsTUFBTUMsS0FBSzNCLEtBQUs0QixHQUFHLEdBQUdDLFFBQVE7WUFDOUIsTUFBTUMsa0JBQWtCO2dCQUN0Qkg7Z0JBQ0FJLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLFVBQVU7Z0JBQ1YsR0FBR1IsWUFBWTtZQUNqQjtZQUVBbEMsSUFBSSxDQUFDYyxRQUFXO29CQUNkSCxlQUFlOzJCQUFJRyxNQUFNSCxhQUFhO3dCQUFFMkI7cUJBQWdCO2dCQUMxRDtZQUVBLDBDQUEwQztZQUMxQyxJQUFJQSxnQkFBZ0JJLFFBQVEsR0FBRyxHQUFHO2dCQUNoQ0MsV0FBVztvQkFDVDFDLE1BQU0yQyxrQkFBa0IsQ0FBQ1Q7Z0JBQzNCLEdBQUdHLGdCQUFnQkksUUFBUTtZQUM3QjtZQUVBLE9BQU9QO1FBQ1Q7UUFFQVMsb0JBQW9CLENBQUNULEtBQU9uQyxJQUFJLENBQUNjLFFBQVc7b0JBQzFDSCxlQUFlRyxNQUFNSCxhQUFhLENBQUNrQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVYLEVBQUUsS0FBS0E7Z0JBQzFEO1FBRUFZLG9CQUFvQixJQUFNL0MsSUFBSTtnQkFBRVcsZUFBZSxFQUFFO1lBQUM7UUFFbEQsaUJBQWlCO1FBQ2pCcUMsa0JBQWtCLENBQUNDLFVBQVlqRCxJQUFJO2dCQUFFWSxlQUFlcUM7WUFBUTtRQUU1RCwyQkFBMkI7UUFDM0JDLGtCQUFrQjtRQUNsQkMsaUJBQWlCLElBQU1uRCxJQUFJLENBQUNjLFFBQVc7b0JBQ3JDb0Msa0JBQWtCLENBQUNwQyxNQUFNb0MsZ0JBQWdCO2dCQUMzQztRQUVBLGVBQWU7UUFDZkUsYUFBYTtRQUNiQyxlQUFlLEVBQUU7UUFDakJDLFlBQVk7UUFFWkMsZ0JBQWdCLENBQUNDLFFBQVV4RCxJQUFJO2dCQUFFb0QsYUFBYUk7WUFBTTtRQUNwREMsa0JBQWtCLENBQUNDLFVBQVkxRCxJQUFJO2dCQUFFcUQsZUFBZUs7WUFBUTtRQUM1REMsY0FBYyxJQUFNM0QsSUFBSSxDQUFDYyxRQUFXO29CQUFFd0MsWUFBWSxDQUFDeEMsTUFBTXdDLFVBQVU7Z0JBQUM7UUFFcEUsbUJBQW1CO1FBQ25CTSxpQkFBaUI7WUFDZkMsUUFBUTtnQkFDTkMsZ0JBQWdCO29CQUFDO29CQUFRO29CQUFlO29CQUFVO2lCQUFPO2dCQUN6REMsY0FBYztZQUNoQjtZQUNBQyxNQUFNO2dCQUNKQyxlQUFlO2dCQUNmQyxTQUFTLE9BQU8sb0NBQW9DO1lBQ3REO1lBQ0FDLFVBQVU7Z0JBQ1JDLGNBQWM7Z0JBQ2RDLFdBQVc7Z0JBQ1hDLFNBQVM7WUFDWDtRQUNGO1FBRUFDLHNCQUFzQixDQUFDaEQsTUFBTWlELEtBQUtDLFFBQVV6RSxJQUFJLENBQUNjLFFBQVc7b0JBQzFEOEMsaUJBQWlCO3dCQUNmLEdBQUc5QyxNQUFNOEMsZUFBZTt3QkFDeEIsQ0FBQ3JDLEtBQUssRUFBRTs0QkFDTixHQUFHVCxNQUFNOEMsZUFBZSxDQUFDckMsS0FBSzs0QkFDOUIsQ0FBQ2lELElBQUksRUFBRUM7d0JBQ1Q7b0JBQ0Y7Z0JBQ0Y7UUFFQSxzQkFBc0I7UUFDdEJDLFdBQVc7WUFDVEMsWUFBWTtZQUNaQyxhQUFhO1lBQ2JDLFVBQVU7UUFDWjtRQUVBQyxjQUFjLENBQUNKLFlBQWMxRSxJQUFJO2dCQUFFMEU7WUFBVTtRQUU3QyxhQUFhO1FBQ2JLLFdBQVc7UUFDWEMsV0FBVztRQUVYQyxnQkFBZ0IsQ0FBQ0MsT0FBTyxJQUFJLEdBQUtsRixJQUFJO2dCQUNuQytFLFdBQVc7Z0JBQ1hDLFdBQVdFO2dCQUNYaEYsYUFBYTtnQkFDYkcsaUJBQWlCO1lBQ25CO1FBRUE4RSxlQUFlLElBQU1uRixJQUFJO2dCQUN2QitFLFdBQVc7Z0JBQ1hDLFdBQVc7WUFDYjtRQUVBLHFCQUFxQjtRQUNyQkksY0FBYyxJQUFNcEYsSUFBSTtnQkFDdEJFLGFBQWE7Z0JBQ2JDLGVBQWU7Z0JBQ2ZDLGVBQWU7Z0JBQ2ZDLGlCQUFpQjtnQkFDakJpRCxZQUFZO2dCQUNaRixhQUFhO2dCQUNiQyxlQUFlLEVBQUU7Z0JBQ2pCMEIsV0FBVztnQkFDWEMsV0FBVztnQkFDWHJFLGVBQWUsRUFBRTtZQUNuQjtJQUNGLElBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb2N1c2Zsb3ctYWkvLi9zdG9yZS91aVN0b3JlLmpzPzk2NTUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gWnVzdGFuZCBzdG9yZSBmb3IgVUkgc3RhdGUgbWFuYWdlbWVudFxyXG5pbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJztcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VVSVN0b3JlID0gY3JlYXRlKChzZXQsIGdldCkgPT4gKHtcclxuICAvLyBTaWRlYmFyIHN0YXRlXHJcbiAgc2lkZWJhck9wZW46IHRydWUsXHJcbiAgXHJcbiAgLy8gTW9kYWwgc3RhdGVzXHJcbiAgdGFza01vZGFsT3BlbjogZmFsc2UsXHJcbiAgdGFza01vZGFsRGF0YTogbnVsbCxcclxuICBcclxuICAvLyBBSSBBc3Npc3RhbnQgc3RhdGVcclxuICBhaUFzc2lzdGFudE9wZW46IGZhbHNlLFxyXG4gIFxyXG4gIC8vIENhbGVuZGFyIHN0YXRlXHJcbiAgY2FsZW5kYXJWaWV3OiAnbW9udGgnLCAvLyBtb250aCwgd2VlaywgZGF5XHJcbiAgc2VsZWN0ZWREYXRlOiBuZXcgRGF0ZSgpLFxyXG4gIFxyXG4gIC8vIFRoZW1lIGFuZCBwcmVmZXJlbmNlc1xyXG4gIHRoZW1lOiAnbGlnaHQnLCAvLyBsaWdodCwgZGFyaywgc3lzdGVtXHJcbiAgY29tcGFjdE1vZGU6IGZhbHNlLFxyXG4gIFxyXG4gIC8vIE5vdGlmaWNhdGlvbiBzdGF0ZVxyXG4gIG5vdGlmaWNhdGlvbnM6IFtdLFxyXG4gIFxyXG4gIC8vIExvYWRpbmcgc3RhdGVzXHJcbiAgZ2xvYmFsTG9hZGluZzogZmFsc2UsXHJcbiAgXHJcbiAgLy8gQWN0aW9uc1xyXG4gIHRvZ2dsZVNpZGViYXI6ICgpID0+IHNldCgoc3RhdGUpID0+ICh7IHNpZGViYXJPcGVuOiAhc3RhdGUuc2lkZWJhck9wZW4gfSkpLFxyXG4gIHNldFNpZGViYXJPcGVuOiAob3BlbikgPT4gc2V0KHsgc2lkZWJhck9wZW46IG9wZW4gfSksXHJcbiAgXHJcbiAgLy8gVGFzayBtb2RhbCBhY3Rpb25zXHJcbiAgb3BlblRhc2tNb2RhbDogKHRhc2tEYXRhID0gbnVsbCkgPT4gc2V0KHsgXHJcbiAgICB0YXNrTW9kYWxPcGVuOiB0cnVlLCBcclxuICAgIHRhc2tNb2RhbERhdGE6IHRhc2tEYXRhIFxyXG4gIH0pLFxyXG4gIGNsb3NlVGFza01vZGFsOiAoKSA9PiBzZXQoeyBcclxuICAgIHRhc2tNb2RhbE9wZW46IGZhbHNlLCBcclxuICAgIHRhc2tNb2RhbERhdGE6IG51bGwgXHJcbiAgfSksXHJcbiAgXHJcbiAgLy8gQUkgQXNzaXN0YW50IGFjdGlvbnNcclxuICB0b2dnbGVBSUFzc2lzdGFudDogKCkgPT4gc2V0KChzdGF0ZSkgPT4gKHsgXHJcbiAgICBhaUFzc2lzdGFudE9wZW46ICFzdGF0ZS5haUFzc2lzdGFudE9wZW4gXHJcbiAgfSkpLFxyXG4gIHNldEFJQXNzaXN0YW50T3BlbjogKG9wZW4pID0+IHNldCh7IGFpQXNzaXN0YW50T3Blbjogb3BlbiB9KSxcclxuICBcclxuICAvLyBDYWxlbmRhciBhY3Rpb25zXHJcbiAgc2V0Q2FsZW5kYXJWaWV3OiAodmlldykgPT4gc2V0KHsgY2FsZW5kYXJWaWV3OiB2aWV3IH0pLFxyXG4gIHNldFNlbGVjdGVkRGF0ZTogKGRhdGUpID0+IHNldCh7IHNlbGVjdGVkRGF0ZTogZGF0ZSB9KSxcclxuICBcclxuICAvLyBUaGVtZSBhY3Rpb25zXHJcbiAgc2V0VGhlbWU6ICh0aGVtZSkgPT4ge1xyXG4gICAgc2V0KHsgdGhlbWUgfSk7XHJcbiAgICAvLyBBcHBseSB0aGVtZSB0byBkb2N1bWVudFxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgIGlmICh0aGVtZSA9PT0gJ2RhcmsnKSB7XHJcbiAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5hZGQoJ2RhcmsnKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LnJlbW92ZSgnZGFyaycpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSxcclxuICBcclxuICB0b2dnbGVDb21wYWN0TW9kZTogKCkgPT4gc2V0KChzdGF0ZSkgPT4gKHsgY29tcGFjdE1vZGU6ICFzdGF0ZS5jb21wYWN0TW9kZSB9KSksXHJcbiAgXHJcbiAgLy8gTm90aWZpY2F0aW9uIGFjdGlvbnNcclxuICBhZGROb3RpZmljYXRpb246IChub3RpZmljYXRpb24pID0+IHtcclxuICAgIGNvbnN0IGlkID0gRGF0ZS5ub3coKS50b1N0cmluZygpO1xyXG4gICAgY29uc3QgbmV3Tm90aWZpY2F0aW9uID0ge1xyXG4gICAgICBpZCxcclxuICAgICAgdHlwZTogJ2luZm8nLCAvLyBpbmZvLCBzdWNjZXNzLCB3YXJuaW5nLCBlcnJvclxyXG4gICAgICB0aXRsZTogJycsXHJcbiAgICAgIG1lc3NhZ2U6ICcnLFxyXG4gICAgICBkdXJhdGlvbjogNTAwMCxcclxuICAgICAgLi4ubm90aWZpY2F0aW9uXHJcbiAgICB9O1xyXG4gICAgXHJcbiAgICBzZXQoKHN0YXRlKSA9PiAoe1xyXG4gICAgICBub3RpZmljYXRpb25zOiBbLi4uc3RhdGUubm90aWZpY2F0aW9ucywgbmV3Tm90aWZpY2F0aW9uXVxyXG4gICAgfSkpO1xyXG4gICAgXHJcbiAgICAvLyBBdXRvLXJlbW92ZSBub3RpZmljYXRpb24gYWZ0ZXIgZHVyYXRpb25cclxuICAgIGlmIChuZXdOb3RpZmljYXRpb24uZHVyYXRpb24gPiAwKSB7XHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIGdldCgpLnJlbW92ZU5vdGlmaWNhdGlvbihpZCk7XHJcbiAgICAgIH0sIG5ld05vdGlmaWNhdGlvbi5kdXJhdGlvbik7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBpZDtcclxuICB9LFxyXG4gIFxyXG4gIHJlbW92ZU5vdGlmaWNhdGlvbjogKGlkKSA9PiBzZXQoKHN0YXRlKSA9PiAoe1xyXG4gICAgbm90aWZpY2F0aW9uczogc3RhdGUubm90aWZpY2F0aW9ucy5maWx0ZXIobiA9PiBuLmlkICE9PSBpZClcclxuICB9KSksXHJcbiAgXHJcbiAgY2xlYXJOb3RpZmljYXRpb25zOiAoKSA9PiBzZXQoeyBub3RpZmljYXRpb25zOiBbXSB9KSxcclxuICBcclxuICAvLyBHbG9iYWwgbG9hZGluZ1xyXG4gIHNldEdsb2JhbExvYWRpbmc6IChsb2FkaW5nKSA9PiBzZXQoeyBnbG9iYWxMb2FkaW5nOiBsb2FkaW5nIH0pLFxyXG4gIFxyXG4gIC8vIEtleWJvYXJkIHNob3J0Y3V0cyBzdGF0ZVxyXG4gIHNob3J0Y3V0c0VuYWJsZWQ6IHRydWUsXHJcbiAgdG9nZ2xlU2hvcnRjdXRzOiAoKSA9PiBzZXQoKHN0YXRlKSA9PiAoeyBcclxuICAgIHNob3J0Y3V0c0VuYWJsZWQ6ICFzdGF0ZS5zaG9ydGN1dHNFbmFibGVkIFxyXG4gIH0pKSxcclxuICBcclxuICAvLyBTZWFyY2ggc3RhdGVcclxuICBzZWFyY2hRdWVyeTogJycsXHJcbiAgc2VhcmNoUmVzdWx0czogW10sXHJcbiAgc2VhcmNoT3BlbjogZmFsc2UsXHJcbiAgXHJcbiAgc2V0U2VhcmNoUXVlcnk6IChxdWVyeSkgPT4gc2V0KHsgc2VhcmNoUXVlcnk6IHF1ZXJ5IH0pLFxyXG4gIHNldFNlYXJjaFJlc3VsdHM6IChyZXN1bHRzKSA9PiBzZXQoeyBzZWFyY2hSZXN1bHRzOiByZXN1bHRzIH0pLFxyXG4gIHRvZ2dsZVNlYXJjaDogKCkgPT4gc2V0KChzdGF0ZSkgPT4gKHsgc2VhcmNoT3BlbjogIXN0YXRlLnNlYXJjaE9wZW4gfSkpLFxyXG4gIFxyXG4gIC8vIFZpZXcgcHJlZmVyZW5jZXNcclxuICB2aWV3UHJlZmVyZW5jZXM6IHtcclxuICAgIGthbmJhbjoge1xyXG4gICAgICBjb2x1bW5zVmlzaWJsZTogWyd0b2RvJywgJ2luLXByb2dyZXNzJywgJ3JldmlldycsICdkb25lJ10sXHJcbiAgICAgIGNvbXBhY3RDYXJkczogZmFsc2VcclxuICAgIH0sXHJcbiAgICB0b2RvOiB7XHJcbiAgICAgIHNob3dDb21wbGV0ZWQ6IGZhbHNlLFxyXG4gICAgICBncm91cEJ5OiAnbm9uZScgLy8gbm9uZSwgcHJpb3JpdHksIGNhdGVnb3J5LCBkdWVEYXRlXHJcbiAgICB9LFxyXG4gICAgY2FsZW5kYXI6IHtcclxuICAgICAgc2hvd1dlZWtlbmRzOiB0cnVlLFxyXG4gICAgICBzdGFydEhvdXI6IDgsXHJcbiAgICAgIGVuZEhvdXI6IDE4XHJcbiAgICB9XHJcbiAgfSxcclxuICBcclxuICB1cGRhdGVWaWV3UHJlZmVyZW5jZTogKHZpZXcsIGtleSwgdmFsdWUpID0+IHNldCgoc3RhdGUpID0+ICh7XHJcbiAgICB2aWV3UHJlZmVyZW5jZXM6IHtcclxuICAgICAgLi4uc3RhdGUudmlld1ByZWZlcmVuY2VzLFxyXG4gICAgICBbdmlld106IHtcclxuICAgICAgICAuLi5zdGF0ZS52aWV3UHJlZmVyZW5jZXNbdmlld10sXHJcbiAgICAgICAgW2tleV06IHZhbHVlXHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9KSksXHJcbiAgXHJcbiAgLy8gRHJhZyBhbmQgZHJvcCBzdGF0ZVxyXG4gIGRyYWdTdGF0ZToge1xyXG4gICAgaXNEcmFnZ2luZzogZmFsc2UsXHJcbiAgICBkcmFnZ2VkSXRlbTogbnVsbCxcclxuICAgIGRyb3Bab25lOiBudWxsXHJcbiAgfSxcclxuICBcclxuICBzZXREcmFnU3RhdGU6IChkcmFnU3RhdGUpID0+IHNldCh7IGRyYWdTdGF0ZSB9KSxcclxuICBcclxuICAvLyBGb2N1cyBtb2RlXHJcbiAgZm9jdXNNb2RlOiBmYWxzZSxcclxuICBmb2N1c1Rhc2s6IG51bGwsXHJcbiAgXHJcbiAgZW50ZXJGb2N1c01vZGU6ICh0YXNrID0gbnVsbCkgPT4gc2V0KHsgXHJcbiAgICBmb2N1c01vZGU6IHRydWUsIFxyXG4gICAgZm9jdXNUYXNrOiB0YXNrLFxyXG4gICAgc2lkZWJhck9wZW46IGZhbHNlLFxyXG4gICAgYWlBc3Npc3RhbnRPcGVuOiBmYWxzZVxyXG4gIH0pLFxyXG4gIFxyXG4gIGV4aXRGb2N1c01vZGU6ICgpID0+IHNldCh7IFxyXG4gICAgZm9jdXNNb2RlOiBmYWxzZSwgXHJcbiAgICBmb2N1c1Rhc2s6IG51bGwgXHJcbiAgfSksXHJcbiAgXHJcbiAgLy8gUmVzZXQgYWxsIFVJIHN0YXRlXHJcbiAgcmVzZXRVSVN0YXRlOiAoKSA9PiBzZXQoe1xyXG4gICAgc2lkZWJhck9wZW46IHRydWUsXHJcbiAgICB0YXNrTW9kYWxPcGVuOiBmYWxzZSxcclxuICAgIHRhc2tNb2RhbERhdGE6IG51bGwsXHJcbiAgICBhaUFzc2lzdGFudE9wZW46IGZhbHNlLFxyXG4gICAgc2VhcmNoT3BlbjogZmFsc2UsXHJcbiAgICBzZWFyY2hRdWVyeTogJycsXHJcbiAgICBzZWFyY2hSZXN1bHRzOiBbXSxcclxuICAgIGZvY3VzTW9kZTogZmFsc2UsXHJcbiAgICBmb2N1c1Rhc2s6IG51bGwsXHJcbiAgICBub3RpZmljYXRpb25zOiBbXVxyXG4gIH0pXHJcbn0pKTsiXSwibmFtZXMiOlsiY3JlYXRlIiwidXNlVUlTdG9yZSIsInNldCIsImdldCIsInNpZGViYXJPcGVuIiwidGFza01vZGFsT3BlbiIsInRhc2tNb2RhbERhdGEiLCJhaUFzc2lzdGFudE9wZW4iLCJjYWxlbmRhclZpZXciLCJzZWxlY3RlZERhdGUiLCJEYXRlIiwidGhlbWUiLCJjb21wYWN0TW9kZSIsIm5vdGlmaWNhdGlvbnMiLCJnbG9iYWxMb2FkaW5nIiwidG9nZ2xlU2lkZWJhciIsInN0YXRlIiwic2V0U2lkZWJhck9wZW4iLCJvcGVuIiwib3BlblRhc2tNb2RhbCIsInRhc2tEYXRhIiwiY2xvc2VUYXNrTW9kYWwiLCJ0b2dnbGVBSUFzc2lzdGFudCIsInNldEFJQXNzaXN0YW50T3BlbiIsInNldENhbGVuZGFyVmlldyIsInZpZXciLCJzZXRTZWxlY3RlZERhdGUiLCJkYXRlIiwic2V0VGhlbWUiLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsImNsYXNzTGlzdCIsImFkZCIsInJlbW92ZSIsInRvZ2dsZUNvbXBhY3RNb2RlIiwiYWRkTm90aWZpY2F0aW9uIiwibm90aWZpY2F0aW9uIiwiaWQiLCJub3ciLCJ0b1N0cmluZyIsIm5ld05vdGlmaWNhdGlvbiIsInR5cGUiLCJ0aXRsZSIsIm1lc3NhZ2UiLCJkdXJhdGlvbiIsInNldFRpbWVvdXQiLCJyZW1vdmVOb3RpZmljYXRpb24iLCJmaWx0ZXIiLCJuIiwiY2xlYXJOb3RpZmljYXRpb25zIiwic2V0R2xvYmFsTG9hZGluZyIsImxvYWRpbmciLCJzaG9ydGN1dHNFbmFibGVkIiwidG9nZ2xlU2hvcnRjdXRzIiwic2VhcmNoUXVlcnkiLCJzZWFyY2hSZXN1bHRzIiwic2VhcmNoT3BlbiIsInNldFNlYXJjaFF1ZXJ5IiwicXVlcnkiLCJzZXRTZWFyY2hSZXN1bHRzIiwicmVzdWx0cyIsInRvZ2dsZVNlYXJjaCIsInZpZXdQcmVmZXJlbmNlcyIsImthbmJhbiIsImNvbHVtbnNWaXNpYmxlIiwiY29tcGFjdENhcmRzIiwidG9kbyIsInNob3dDb21wbGV0ZWQiLCJncm91cEJ5IiwiY2FsZW5kYXIiLCJzaG93V2Vla2VuZHMiLCJzdGFydEhvdXIiLCJlbmRIb3VyIiwidXBkYXRlVmlld1ByZWZlcmVuY2UiLCJrZXkiLCJ2YWx1ZSIsImRyYWdTdGF0ZSIsImlzRHJhZ2dpbmciLCJkcmFnZ2VkSXRlbSIsImRyb3Bab25lIiwic2V0RHJhZ1N0YXRlIiwiZm9jdXNNb2RlIiwiZm9jdXNUYXNrIiwiZW50ZXJGb2N1c01vZGUiLCJ0YXNrIiwiZXhpdEZvY3VzTW9kZSIsInJlc2V0VUlTdGF0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./store/uiStore.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "zustand":
/*!**************************!*\
  !*** external "zustand" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_app.js"));
module.exports = __webpack_exports__;

})();