// AI Assistant chat interface with command processing
import { useState, useRef, useEffect } from 'react';
import { openRouterClient } from '../../lib/openrouter-config';
import { useTaskStore } from '../../store/taskStore';
import { useAIStore } from '../../store/aiStore';
import ChatMessage from './ChatMessage';

export default function AIAssistant({ userId }) {
  const [input, setInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const messagesEndRef = useRef(null);
  
  const { createTask, updateTask, tasks } = useTaskStore();
  const { messages, addMessage, clearMessages } = useAIStore();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Process user input and generate AI response
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim() || isProcessing) return;

    const userMessage = input.trim();
    setInput('');
    setIsProcessing(true);

    // Add user message to chat
    addMessage({
      id: Date.now(),
      content: userMessage,
      isUser: true,
      timestamp: new Date().toISOString()
    });

    try {
      // Check if this is a task-related command
      if (isTaskCommand(userMessage)) {
        await processTaskCommand(userMessage);
      } else {
        // General conversation
        await processGeneralChat(userMessage);
      }
    } catch (error) {
      console.error('AI processing error:', error);
      addMessage({
        id: Date.now() + 1,
        content: 'Sorry, I encountered an error processing your request. Please try again.',
        isUser: false,
        timestamp: new Date().toISOString(),
        isError: true
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Check if user input is a task-related command
  const isTaskCommand = (input) => {
    const taskKeywords = [
      'create', 'add', 'new task', 'schedule', 'remind', 'due',
      'update', 'edit', 'change', 'modify', 'complete', 'done',
      'delete', 'remove', 'cancel', 'list', 'show', 'find'
    ];
    
    return taskKeywords.some(keyword => 
      input.toLowerCase().includes(keyword)
    );
  };

  // Process task-related commands
  const processTaskCommand = async (userInput) => {
    try {
      // Get AI to parse the command
      const response = await openRouterClient.processTaskCommand(userInput, {
        userId,
        currentTasks: tasks.slice(0, 5) // Provide context of recent tasks
      });

      // Parse AI response
      let aiResponse;
      try {
        aiResponse = JSON.parse(response);
      } catch {
        // If not JSON, treat as regular response
        addMessage({
          id: Date.now() + 1,
          content: response,
          isUser: false,
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Execute the parsed command
      await executeTaskCommand(aiResponse, userInput);
      
    } catch (error) {
      console.error('Task command processing error:', error);
      throw error;
    }
  };

  // Execute parsed task commands
  const executeTaskCommand = async (aiResponse, originalInput) => {
    const { action, data, confidence } = aiResponse;

    // Low confidence - ask for clarification
    if (confidence < 0.7) {
      addMessage({
        id: Date.now() + 1,
        content: `I'm not entirely sure what you want to do. Could you be more specific? For example: "Create a task called 'Review presentation' due tomorrow with high priority"`,
        isUser: false,
        timestamp: new Date().toISOString()
      });
      return;
    }

    let responseMessage = '';

    try {
      switch (action) {
        case 'create_task':
          const newTask = {
            ...data,
            id: Date.now().toString(),
            userId,
            status: 'todo',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          
          await createTask(newTask);
          responseMessage = `✅ Created task: "${data.title}"${data.dueDate ? ` (due ${new Date(data.dueDate).toLocaleDateString()})` : ''}`;
          break;

        case 'list_tasks':
          const activeTasks = tasks.filter(t => t.status !== 'done').slice(0, 5);
          responseMessage = activeTasks.length > 0 
            ? `📋 Your active tasks:\n${activeTasks.map(t => `• ${t.title} (${t.status})`).join('\n')}`
            : '🎉 No active tasks! You\'re all caught up.';
          break;

        case 'update_task':
          // Implementation for updating tasks
          responseMessage = `✏️ Task updated successfully.`;
          break;

        default:
          responseMessage = `I understand you want to ${action}, but I need more specific information to help you.`;
      }
    } catch (error) {
      responseMessage = `❌ Sorry, I couldn't complete that action. ${error.message}`;
    }

    // Add AI response to chat
    addMessage({
      id: Date.now() + 1,
      content: responseMessage,
      isUser: false,
      timestamp: new Date().toISOString(),
      actionPerformed: action
    });
  };

  // Process general conversation
  const processGeneralChat = async (userInput) => {
    const conversationHistory = messages.slice(-5).map(msg => ({
      role: msg.isUser ? 'user' : 'assistant',
      content: msg.content
    }));

    conversationHistory.push({
      role: 'user',
      content: userInput
    });

    const response = await openRouterClient.chat(conversationHistory);
    
    addMessage({
      id: Date.now() + 1,
      content: response,
      isUser: false,
      timestamp: new Date().toISOString()
    });
  };

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-gray-900">AI Assistant</h3>
            <p className="text-xs text-gray-500">Your productivity companion</p>
          </div>
          <button
            onClick={clearMessages}
            className="text-gray-400 hover:text-gray-600 text-sm"
          >
            Clear
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            <p className="mb-4">👋 Hi! I'm your AI assistant.</p>
            <div className="text-sm space-y-1">
              <p>Try saying:</p>
              <p className="text-blue-600">"Create a task called 'Review presentation'"</p>
              <p className="text-blue-600">"Show me my active tasks"</p>
              <p className="text-blue-600">"Schedule a meeting for tomorrow"</p>
            </div>
          </div>
        )}
        
        {messages.map((message) => (
          <ChatMessage key={message.id} message={message} />
        ))}
        
        {isProcessing && (
          <div className="flex items-center space-x-2 text-gray-500">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm">AI is thinking...</span>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <form onSubmit={handleSubmit} className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask me anything or give me a command..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isProcessing}
          />
          <button
            type="submit"
            disabled={!input.trim() || isProcessing}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Send
          </button>
        </div>
      </form>
    </div>
  );
}