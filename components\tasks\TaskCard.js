// Individual task card component with drag-and-drop support
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useState } from 'react';

// Priority color mapping
const PRIORITY_COLORS = {
  high: 'border-red-200 bg-red-50',
  medium: 'border-yellow-200 bg-yellow-50',
  low: 'border-green-200 bg-green-50'
};

// Priority icons
const PriorityIcon = ({ priority }) => {
  const colors = {
    high: 'text-red-500',
    medium: 'text-yellow-500',
    low: 'text-green-500'
  };

  return (
    <div className={`w-2 h-2 rounded-full ${colors[priority] || 'bg-gray-300'}`} />
  );
};

export default function TaskCard({ task, isDragging = false }) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id: task.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Format due date
  const formatDueDate = (dateString) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return { text: 'Overdue', color: 'text-red-600' };
    if (diffDays === 0) return { text: 'Due today', color: 'text-orange-600' };
    if (diffDays === 1) return { text: 'Due tomorrow', color: 'text-yellow-600' };
    return { text: `Due in ${diffDays} days`, color: 'text-gray-600' };
  };

  const dueDate = formatDueDate(task.dueDate);

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`
        bg-white rounded-lg border-2 p-4 cursor-grab active:cursor-grabbing
        hover:shadow-md transition-all duration-200
        ${PRIORITY_COLORS[task.priority] || 'border-gray-200'}
        ${isDragging || isSortableDragging ? 'opacity-50 shadow-lg' : ''}
      `}
    >
      {/* Task Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center space-x-2">
          <PriorityIcon priority={task.priority} />
          <h4 className="font-medium text-gray-900 text-sm line-clamp-2">
            {task.title}
          </h4>
        </div>
        
        {/* Task Menu */}
        <button className="text-gray-400 hover:text-gray-600 p-1">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
          </svg>
        </button>
      </div>

      {/* Task Description */}
      {task.description && (
        <p className={`text-gray-600 text-xs mb-3 ${
          isExpanded ? '' : 'line-clamp-2'
        }`}>
          {task.description}
        </p>
      )}

      {/* Task Tags */}
      {task.tags && task.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-3">
          {task.tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
            >
              {tag}
            </span>
          ))}
          {task.tags.length > 3 && (
            <span className="text-gray-500 text-xs">
              +{task.tags.length - 3} more
            </span>
          )}
        </div>
      )}

      {/* Task Footer */}
      <div className="flex items-center justify-between text-xs">
        {/* Due Date */}
        {dueDate && (
          <span className={`font-medium ${dueDate.color}`}>
            {dueDate.text}
          </span>
        )}
        
        {/* Category Badge */}
        {task.category && (
          <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full capitalize">
            {task.category}
          </span>
        )}
      </div>

      {/* Progress Bar (if task has subtasks) */}
      {task.subtasks && task.subtasks.length > 0 && (
        <div className="mt-3">
          <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
            <span>Progress</span>
            <span>
              {task.subtasks.filter(sub => sub.completed).length}/{task.subtasks.length}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div
              className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
              style={{
                width: `${(task.subtasks.filter(sub => sub.completed).length / task.subtasks.length) * 100}%`
              }}
            />
          </div>
        </div>
      )}

      {/* Expand/Collapse Button */}
      {(task.description?.length > 100 || (task.subtasks && task.subtasks.length > 0)) && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            setIsExpanded(!isExpanded);
          }}
          className="mt-2 text-blue-600 text-xs hover:text-blue-800 transition-colors"
        >
          {isExpanded ? 'Show less' : 'Show more'}
        </button>
      )}
    </div>
  );
}