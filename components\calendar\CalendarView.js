// Calendar view component using react-big-calendar
import { useState, useMemo } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';

const localizer = momentLocalizer(moment);

export default function CalendarView({ tasks, userId }) {
  const [view, setView] = useState('month');
  const [date, setDate] = useState(new Date());

  // Convert tasks to calendar events
  const events = useMemo(() => {
    return tasks
      .filter(task => task.dueDate)
      .map(task => ({
        id: task.id,
        title: task.title,
        start: new Date(task.dueDate),
        end: new Date(task.dueDate),
        resource: task,
        allDay: true
      }));
  }, [tasks]);

  // Custom event style getter
  const eventStyleGetter = (event) => {
    const task = event.resource;
    let backgroundColor = '#3174ad';
    
    switch (task.priority) {
      case 'high':
        backgroundColor = '#dc2626';
        break;
      case 'medium':
        backgroundColor = '#f59e0b';
        break;
      case 'low':
        backgroundColor = '#10b981';
        break;
    }

    if (task.status === 'done') {
      backgroundColor = '#6b7280';
    }

    return {
      style: {
        backgroundColor,
        borderRadius: '4px',
        opacity: task.status === 'done' ? 0.6 : 1,
        color: 'white',
        border: '0px',
        display: 'block'
      }
    };
  };

  // Handle event selection
  const handleSelectEvent = (event) => {
    console.log('Selected event:', event);
    // Open task modal or details
  };

  // Handle slot selection (for creating new tasks)
  const handleSelectSlot = ({ start, end }) => {
    console.log('Selected slot:', { start, end });
    // Open create task modal with pre-filled date
  };

  return (
    <div className="h-full bg-white rounded-lg shadow-sm">
      {/* Calendar Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Calendar</h2>
            <p className="text-sm text-gray-600">
              {events.length} {events.length === 1 ? 'task' : 'tasks'} scheduled
            </p>
          </div>

          {/* View switcher */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {['month', 'week', 'day'].map((viewType) => (
              <button
                key={viewType}
                onClick={() => setView(viewType)}
                className={`px-3 py-1 text-sm rounded-md transition-colors capitalize ${
                  view === viewType
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {viewType}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Calendar */}
      <div className="p-4" style={{ height: 'calc(100% - 80px)' }}>
        <Calendar
          localizer={localizer}
          events={events}
          startAccessor="start"
          endAccessor="end"
          view={view}
          onView={setView}
          date={date}
          onNavigate={setDate}
          onSelectEvent={handleSelectEvent}
          onSelectSlot={handleSelectSlot}
          selectable
          eventPropGetter={eventStyleGetter}
          style={{ height: '100%' }}
          views={['month', 'week', 'day']}
          step={60}
          showMultiDayTimes
          components={{
            event: CustomEvent,
            toolbar: CustomToolbar
          }}
        />
      </div>
    </div>
  );
}

// Custom event component
function CustomEvent({ event }) {
  const task = event.resource;
  
  return (
    <div className="p-1">
      <div className="font-medium text-xs truncate">
        {task.title}
      </div>
      {task.priority && (
        <div className="text-xs opacity-75 capitalize">
          {task.priority}
        </div>
      )}
    </div>
  );
}

// Custom toolbar component
function CustomToolbar({ label, onNavigate, onView, view }) {
  return (
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center space-x-2">
        <button
          onClick={() => onNavigate('PREV')}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        
        <button
          onClick={() => onNavigate('TODAY')}
          className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
        >
          Today
        </button>
        
        <button
          onClick={() => onNavigate('NEXT')}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      <h3 className="text-lg font-semibold text-gray-900">{label}</h3>

      <div className="flex items-center space-x-2">
        <span className="text-sm text-gray-600">View:</span>
        <select
          value={view}
          onChange={(e) => onView(e.target.value)}
          className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="month">Month</option>
          <option value="week">Week</option>
          <option value="day">Day</option>
        </select>
      </div>
    </div>
  );
}