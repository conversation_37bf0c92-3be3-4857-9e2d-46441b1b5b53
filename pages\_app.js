// Main app wrapper with providers and global setup
import '../styles/globals.css';
import { useEffect } from 'react';
import { useUIStore } from '../store/uiStore';

export default function App({ Component, pageProps }) {
  const { setTheme } = useUIStore();

  // Initialize theme on app load
  useEffect(() => {
    // Check for saved theme preference or default to 'light'
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
  }, [setTheme]);

  // Global error boundary
  useEffect(() => {
    const handleError = (event) => {
      console.error('Global error:', event.error);
      // You could add error reporting here
    };

    const handleUnhandledRejection = (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      // You could add error reporting here
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return <Component {...pageProps} />;
}
