// Zustand store for UI state management
import { create } from 'zustand';

export const useUIStore = create((set, get) => ({
  // Sidebar state
  sidebarOpen: true,
  
  // Modal states
  taskModalOpen: false,
  taskModalData: null,
  
  // AI Assistant state
  aiAssistantOpen: false,
  
  // Calendar state
  calendarView: 'month', // month, week, day
  selectedDate: new Date(),
  
  // Theme and preferences
  theme: 'light', // light, dark, system
  compactMode: false,
  
  // Notification state
  notifications: [],
  
  // Loading states
  globalLoading: false,
  
  // Actions
  toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),
  setSidebarOpen: (open) => set({ sidebarOpen: open }),
  
  // Task modal actions
  openTaskModal: (taskData = null) => set({ 
    taskModalOpen: true, 
    taskModalData: taskData 
  }),
  closeTaskModal: () => set({ 
    taskModalOpen: false, 
    taskModalData: null 
  }),
  
  // AI Assistant actions
  toggleAIAssistant: () => set((state) => ({ 
    aiAssistantOpen: !state.aiAssistantOpen 
  })),
  setAIAssistantOpen: (open) => set({ aiAssistantOpen: open }),
  
  // Calendar actions
  setCalendarView: (view) => set({ calendarView: view }),
  setSelectedDate: (date) => set({ selectedDate: date }),
  
  // Theme actions
  setTheme: (theme) => {
    set({ theme });
    // Apply theme to document
    if (typeof window !== 'undefined') {
      if (theme === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  },
  
  toggleCompactMode: () => set((state) => ({ compactMode: !state.compactMode })),
  
  // Notification actions
  addNotification: (notification) => {
    const id = Date.now().toString();
    const newNotification = {
      id,
      type: 'info', // info, success, warning, error
      title: '',
      message: '',
      duration: 5000,
      ...notification
    };
    
    set((state) => ({
      notifications: [...state.notifications, newNotification]
    }));
    
    // Auto-remove notification after duration
    if (newNotification.duration > 0) {
      setTimeout(() => {
        get().removeNotification(id);
      }, newNotification.duration);
    }
    
    return id;
  },
  
  removeNotification: (id) => set((state) => ({
    notifications: state.notifications.filter(n => n.id !== id)
  })),
  
  clearNotifications: () => set({ notifications: [] }),
  
  // Global loading
  setGlobalLoading: (loading) => set({ globalLoading: loading }),
  
  // Keyboard shortcuts state
  shortcutsEnabled: true,
  toggleShortcuts: () => set((state) => ({ 
    shortcutsEnabled: !state.shortcutsEnabled 
  })),
  
  // Search state
  searchQuery: '',
  searchResults: [],
  searchOpen: false,
  
  setSearchQuery: (query) => set({ searchQuery: query }),
  setSearchResults: (results) => set({ searchResults: results }),
  toggleSearch: () => set((state) => ({ searchOpen: !state.searchOpen })),
  
  // View preferences
  viewPreferences: {
    kanban: {
      columnsVisible: ['todo', 'in-progress', 'review', 'done'],
      compactCards: false
    },
    todo: {
      showCompleted: false,
      groupBy: 'none' // none, priority, category, dueDate
    },
    calendar: {
      showWeekends: true,
      startHour: 8,
      endHour: 18
    }
  },
  
  updateViewPreference: (view, key, value) => set((state) => ({
    viewPreferences: {
      ...state.viewPreferences,
      [view]: {
        ...state.viewPreferences[view],
        [key]: value
      }
    }
  })),
  
  // Drag and drop state
  dragState: {
    isDragging: false,
    draggedItem: null,
    dropZone: null
  },
  
  setDragState: (dragState) => set({ dragState }),
  
  // Focus mode
  focusMode: false,
  focusTask: null,
  
  enterFocusMode: (task = null) => set({ 
    focusMode: true, 
    focusTask: task,
    sidebarOpen: false,
    aiAssistantOpen: false
  }),
  
  exitFocusMode: () => set({ 
    focusMode: false, 
    focusTask: null 
  }),
  
  // Reset all UI state
  resetUIState: () => set({
    sidebarOpen: true,
    taskModalOpen: false,
    taskModalData: null,
    aiAssistantOpen: false,
    searchOpen: false,
    searchQuery: '',
    searchResults: [],
    focusMode: false,
    focusTask: null,
    notifications: []
  })
}));