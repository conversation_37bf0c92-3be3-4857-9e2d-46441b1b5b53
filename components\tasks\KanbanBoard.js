// Kanban board with drag-and-drop functionality using dnd-kit
import { useState, useMemo } from 'react';
import {
  DndContext,
  DragOverlay,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners
} from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove
} from '@dnd-kit/sortable';
import TaskCard from './TaskCard';
import KanbanColumn from './KanbanColumn';
import { useTaskStore } from '../../store/taskStore';

// Kanban column definitions
const COLUMNS = [
  { id: 'todo', title: 'To Do', color: 'bg-gray-100' },
  { id: 'in-progress', title: 'In Progress', color: 'bg-blue-100' },
  { id: 'review', title: 'Review', color: 'bg-yellow-100' },
  { id: 'done', title: 'Done', color: 'bg-green-100' }
];

export default function KanbanBoard({ tasks, userId }) {
  const [activeId, setActiveId] = useState(null);
  const { updateTask } = useTaskStore();
  
  // Configure drag sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Minimum distance before drag starts
      },
    })
  );

  // Group tasks by status for columns
  const tasksByStatus = useMemo(() => {
    const grouped = {};
    COLUMNS.forEach(column => {
      grouped[column.id] = tasks.filter(task => task.status === column.id);
    });
    return grouped;
  }, [tasks]);

  // Handle drag start
  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  // Handle drag end - update task status
  const handleDragEnd = async (event) => {
    const { active, over } = event;
    
    if (!over) {
      setActiveId(null);
      return;
    }

    const activeTask = tasks.find(task => task.id === active.id);
    const overColumn = over.id;

    // Update task status if dropped on different column
    if (activeTask && activeTask.status !== overColumn) {
      try {
        await updateTask(activeTask.id, {
          ...activeTask,
          status: overColumn,
          updatedAt: new Date().toISOString()
        });
      } catch (error) {
        console.error('Failed to update task status:', error);
      }
    }

    setActiveId(null);
  };

  // Find the active task being dragged
  const activeTask = activeId ? tasks.find(task => task.id === activeId) : null;

  return (
    <div className="h-full">
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        {/* Kanban Columns */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 h-full">
          {COLUMNS.map((column) => (
            <KanbanColumn
              key={column.id}
              column={column}
              tasks={tasksByStatus[column.id] || []}
              userId={userId}
            />
          ))}
        </div>

        {/* Drag Overlay */}
        <DragOverlay>
          {activeTask ? (
            <div className="rotate-3 opacity-90">
              <TaskCard task={activeTask} isDragging />
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}