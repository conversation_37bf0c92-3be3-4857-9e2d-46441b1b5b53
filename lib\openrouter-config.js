// OpenRouter AI API configuration for multi-model access
const OPENROUTER_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_OPENROUTER_BASE_URL,
  apiKey: process.env.OPENROUTER_API_KEY,
  defaultModel: 'anthropic/claude-3-haiku',
  fallbackModel: 'openai/gpt-3.5-turbo',
  maxTokens: 1000,
  temperature: 0.7
};

// Available AI models for different use cases
export const AI_MODELS = {
  TASK_CREATION: 'anthropic/claude-3-haiku', // Fast, efficient for task parsing
  CONVERSATION: 'openai/gpt-3.5-turbo', // Good for general chat
  ANALYSIS: 'anthropic/claude-3-sonnet', // Better for complex analysis
  CODING: 'anthropic/claude-3-opus' // Best for code-related tasks
};

// OpenRouter API client
class OpenRouterClient {
  constructor() {
    this.baseURL = OPENROUTER_CONFIG.baseURL;
    this.apiKey = OPENROUTER_CONFIG.apiKey;
  }

  // Main method to send requests to OpenRouter
  async sendRequest(messages, model = OPENROUTER_CONFIG.defaultModel, options = {}) {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
          'X-Title': 'FocusFlow AI'
        },
        body: JSON.stringify({
          model,
          messages,
          max_tokens: options.maxTokens || OPENROUTER_CONFIG.maxTokens,
          temperature: options.temperature || OPENROUTER_CONFIG.temperature,
          stream: options.stream || false
        })
      });

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`);
      }

      const data = await response.json();
      return data.choices[0].message.content;
    } catch (error) {
      console.error('OpenRouter request failed:', error);
      
      // Fallback to different model if primary fails
      if (model !== OPENROUTER_CONFIG.fallbackModel) {
        return this.sendRequest(messages, OPENROUTER_CONFIG.fallbackModel, options);
      }
      
      throw error;
    }
  }

  // Specialized method for task-related AI operations
  async processTaskCommand(userInput, context = {}) {
    const systemPrompt = {
      role: 'system',
      content: `You are FocusFlow AI, an intelligent task management assistant. 
      Parse user commands and return structured JSON responses for task operations.
      
      Available operations: create_task, update_task, delete_task, list_tasks, schedule_task
      
      Response format:
      {
        "action": "create_task",
        "data": {
          "title": "Task title",
          "description": "Task description",
          "priority": "high|medium|low",
          "dueDate": "ISO date string",
          "category": "work|personal|health|learning"
        },
        "confidence": 0.95
      }`
    };

    const userMessage = {
      role: 'user',
      content: `Context: ${JSON.stringify(context)}\nCommand: ${userInput}`
    };

    return this.sendRequest([systemPrompt, userMessage], AI_MODELS.TASK_CREATION);
  }

  // Method for general conversation
  async chat(messages, model = AI_MODELS.CONVERSATION) {
    const systemPrompt = {
      role: 'system',
      content: 'You are FocusFlow AI, a helpful productivity assistant. Be concise and actionable.'
    };

    return this.sendRequest([systemPrompt, ...messages], model);
  }
}

// Export singleton instance
export const openRouterClient = new OpenRouterClient();

// Helper function to format messages for AI
export const formatMessagesForAI = (conversation) => {
  return conversation.map(msg => ({
    role: msg.isUser ? 'user' : 'assistant',
    content: msg.content
  }));
};

export default OpenRouterClient;