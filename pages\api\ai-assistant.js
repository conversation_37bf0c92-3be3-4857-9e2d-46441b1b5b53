// API route for AI assistant requests (server-side)
import { callOpenRouterAPI, processAICommand } from '../../lib/openrouter-config';

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
        const { command, tasks, context } = req.body;

        if (!command) {
            return res.status(400).json({ error: 'Command is required' });
        }

        // Process the AI command
        const response = await processAICommand(command, tasks || [], context || {});

        res.status(200).json(response);
    } catch (error) {
        console.error('AI Assistant API error:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
}