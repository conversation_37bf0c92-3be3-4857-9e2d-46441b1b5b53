// Main dashboard page - FocusFlow AI productivity interface
import { useState, useEffect } from 'react';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth } from '../lib/firebase-config';
import Layout from '../components/layout/Layout';
import Sidebar from '../components/layout/Sidebar';
import KanbanBoard from '../components/tasks/KanbanBoard';
import TodoList from '../components/tasks/TodoList';
import AIAssistant from '../components/ai/AIAssistant';
import CalendarView from '../components/calendar/CalendarView';
import Dashboard from '../components/analytics/Dashboard';
import { useTaskStore } from '../store/taskStore';
import { useUIStore } from '../store/uiStore';

export default function Home() {
  const [user, loading, error] = useAuthState(auth);
  const [activeView, setActiveView] = useState('kanban');
  
  // Zustand stores for state management
  const { tasks, fetchTasks, isLoading } = useTaskStore();
  const { sidebarOpen, aiAssistantOpen, toggleAIAssistant } = useUIStore();

  // Fetch user tasks on component mount
  useEffect(() => {
    if (user) {
      fetchTasks(user.uid);
    }
  }, [user, fetchTasks]);

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading FocusFlow AI...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Welcome to FocusFlow AI</h1>
          <p className="text-gray-600 mb-8">Your Intelligent Productivity Companion</p>
          <a 
            href="/login" 
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Get Started
          </a>
        </div>
      </div>
    );
  }

  // Main dashboard view switcher
  const renderMainContent = () => {
    switch (activeView) {
      case 'kanban':
        return <KanbanBoard tasks={tasks} userId={user.uid} />;
      case 'todo':
        return <TodoList tasks={tasks} userId={user.uid} />;
      case 'calendar':
        return <CalendarView tasks={tasks} userId={user.uid} />;
      case 'analytics':
        return <Dashboard tasks={tasks} userId={user.uid} />;
      default:
        return <KanbanBoard tasks={tasks} userId={user.uid} />;
    }
  };

  return (
    <Layout>
      <div className="flex h-screen bg-gray-50">
        {/* Sidebar Navigation */}
        <Sidebar 
          activeView={activeView} 
          setActiveView={setActiveView}
          isOpen={sidebarOpen}
        />

        {/* Main Content Area */}
        <main className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-16'}`}>
          {/* Header */}
          <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">
                  {activeView.charAt(0).toUpperCase() + activeView.slice(1)}
                </h1>
                <p className="text-sm text-gray-600">
                  Welcome back, {user.displayName || user.email}
                </p>
              </div>
              
              {/* Quick Actions */}
              <div className="flex items-center space-x-4">
                <button
                  onClick={toggleAIAssistant}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <span>AI Assistant</span>
                </button>
              </div>
            </div>
          </header>

          {/* Main Content */}
          <div className="p-6">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading tasks...</p>
                </div>
              </div>
            ) : (
              renderMainContent()
            )}
          </div>
        </main>

        {/* AI Assistant Sidebar */}
        {aiAssistantOpen && (
          <div className="w-80 bg-white border-l border-gray-200 shadow-lg">
            <AIAssistant userId={user.uid} />
          </div>
        )}
      </div>
    </Layout>
  );
}