# 🎯 FocusFlow AI - Your Intelligent Productivity Companion

A modern, AI-powered task management web application built with Next.js, Firebase, and OpenRouter multi-model AI APIs. FocusFlow AI combines intuitive task management with intelligent assistance to boost your productivity.

## ✨ Features

### 🏗️ Core Functionality
- **Smart Task Management** - Create, organize, and track tasks with drag-and-drop Kanban boards
- **AI Assistant** - Natural language task creation and management via OpenRouter API
- **Multiple Views** - Kanban board, Todo list, Calendar, and Analytics dashboard
- **Real-time Sync** - Firebase Firestore for instant data synchronization
- **Data Export** - Export tasks and analytics to CSV/PDF formats

### 🤖 AI-Powered Features
- **Natural Language Processing** - "Create a task called 'Review presentation' due tomorrow"
- **Smart Task Parsing** - Automatically extract priority, category, and due dates
- **Multi-Model Support** - Claude, GPT, and other models via OpenRouter
- **Contextual Assistance** - AI understands your task history and preferences

### 🎨 Modern UI/UX
- **Responsive Design** - Works seamlessly on desktop, tablet, and mobile
- **Dark/Light Mode** - Automatic theme switching based on system preference
- **Drag & Drop** - Intuitive task organization with @dnd-kit
- **Minimalist Design** - Clean, distraction-free interface with Tailwind CSS

### 🔒 Security & Privacy
- **End-to-End Encryption** - Sensitive data encrypted before storage
- **Firebase Authentication** - Secure user management and access control
- **Environment Variables** - API keys and secrets properly secured

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm/yarn
- Firebase project with Firestore enabled
- OpenRouter API account

### Installation

1. **Clone and install dependencies**
```bash
git clone <repository-url>
cd focusflow-ai
npm install
```

2. **Environment Setup**
```bash
cp .env.local.example .env.local
```

3. **Configure environment variables in `.env.local`**
```env
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# OpenRouter AI API
OPENROUTER_API_KEY=your_openrouter_api_key
NEXT_PUBLIC_OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Encryption
NEXT_PUBLIC_ENCRYPTION_KEY=your_32_character_encryption_key
```

4. **Start development server**
```bash
npm run dev
```

5. **Open [http://localhost:3000](http://localhost:3000)**

## 🤖 AI Assistant Demo

### Example Interactions

**User Input:** "Create a task called 'Design Homepage' with high priority due next Friday"

**AI Response:**
```json
{
  "action": "create_task",
  "data": {
    "title": "Design Homepage",
    "description": "",
    "priority": "high",
    "dueDate": "2025-01-24T23:59:59.000Z",
    "category": "work"
  },
  "confidence": 0.95
}
```

**System Action:** ✅ Created task: "Design Homepage" (due 1/24/2025)

---

**User Input:** "Show me my active tasks"

**AI Response:**
```
📋 Your active tasks:
• Design Homepage (todo)
• Review presentation (in-progress)
• Update documentation (todo)
• Team meeting prep (review)
```

---

**User Input:** "Schedule a meeting with the design team for tomorrow at 2 PM"

**AI Response:**
```json
{
  "action": "create_task",
  "data": {
    "title": "Meeting with design team",
    "description": "Scheduled for 2:00 PM",
    "priority": "medium",
    "dueDate": "2025-01-16T14:00:00.000Z",
    "category": "work"
  },
  "confidence": 0.88
}
```

## 🛠️ Implementation Phases

### Phase 1: Foundation ✅
- [x] Firebase authentication and Firestore setup
- [x] End-to-end encryption for sensitive data
- [x] Basic user management and data structure
- [x] Zustand state management setup

### Phase 2: Core UI ✅
- [x] Responsive layout with sidebar navigation
- [x] Kanban board with drag-and-drop functionality
- [x] Todo list view and task CRUD operations
- [x] Task card components with priority/category

### Phase 3: AI Integration ✅
- [x] AI assistant chat interface
- [x] Natural language task creation and management
- [x] Multi-model AI API integration via OpenRouter
- [x] Command processing and context awareness

### Phase 4: Advanced Features ✅
- [x] Calendar view with task scheduling
- [x] Analytics dashboard with productivity metrics
- [x] Data export (CSV/PDF) functionality
- [x] Voice commands preparation (hooks ready)

## 🔧 Available Scripts

```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run start    # Start production server
npm run lint     # Run ESLint
```

## 🎨 Tech Stack

- **Frontend:** Next.js 14, React 18, Tailwind CSS
- **Backend:** Firebase Firestore, Firebase Auth
- **AI:** OpenRouter API (Claude, GPT, etc.)
- **State Management:** Zustand
- **Drag & Drop:** @dnd-kit
- **Calendar:** react-big-calendar
- **Export:** jsPDF, json2csv
- **Styling:** Tailwind CSS, Custom CSS Variables

## 📊 Key Components

### Main Dashboard (`pages/index.js`)
- Unified interface with sidebar navigation
- View switching between Kanban, Todo, Calendar, Analytics
- AI Assistant integration with toggle
- Real-time task synchronization

### AI Assistant (`components/ai/AIAssistant.js`)
- Natural language command processing
- Multi-model AI support via OpenRouter
- Context-aware task operations
- Conversation history and persistence

### Task Management (`components/tasks/`)
- Drag-and-drop Kanban board
- Todo list with filtering and sorting
- Task cards with priority indicators
- CRUD operations with Firebase sync

### Analytics Dashboard (`components/analytics/Dashboard.js`)
- Productivity metrics and insights
- Task completion tracking
- Category and priority distribution
- Export functionality (CSV/PDF)

---

**Built with ❤️ for productivity enthusiasts**

*FocusFlow AI - Where Intelligence Meets Productivity*